<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="org.autojs.autojs6" >

    <uses-sdk android:minSdkVersion="24" />

    <uses-feature
        android:name="android.hardware.telephony"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <!-- @Hint by SuperMonster003 on May 27, 2022. -->
    <!-- ! -->
    <!-- ! WRT: android.permission.WRITE_EXTERNAL_STORAGE -->
    <!-- ! MSV<API>: android:maxSdkVersion="API" -->
    <!-- ! LGC: android:requestLegacyExternalStorage="true" (since API 29) -->
    <!-- ! MAN: android.permission.MANAGE_EXTERNAL_STORAGE (since API 30) -->
    <!-- ! -->
    <!-- ! Storage permission requirement on different API targets. -->
    <!-- ! [ XXX ] means optional but recommended. -->
    <!-- ! zh-CN: -->
    <!-- ! 存储权限对应不同的 API 级别. -->
    <!-- ! [ XXX ] 代表可选但推荐满足其要求. -->
    <!-- ! -->
    <!-- ! Target API == 28: WRT -->
    <!-- ! Target API == 29: WRT + [ MSV<29> ] / WRT + [ MSV<28> ] + LGC -->
    <!-- ! Target API >= 30: WRT + [ MSV<29> ] + LGC  + MAN -->

    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="29" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission
        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission
        android:name="android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS"
        android:maxSdkVersion="31"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.READ_PRIVILEGED_PHONE_STATE"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />
    <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
    <uses-permission
        android:name="android.permission.SCHEDULE_EXACT_ALARM"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <!-- To allow posting notifications on Android 13. -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
    <uses-permission
        android:name="android.permission.UNLIMITED_TOASTS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.CAPTURE_VIDEO_OUTPUT"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.DUMP"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />
    <uses-permission
        android:name="android.permission.PACKAGE_USAGE_STATS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.WRITE_SECURE_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.MANAGE_USERS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.INTERACT_ACROSS_USERS_FULL"
        android:protectionLevel="signature"
        tools:ignore="ProtectedPermissions" />

    <!-- 非 AutoJs6 运行必需, 不会主动申请, 脚本可自行申请 -->

    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="com.termux.permission.RUN_COMMAND" />

    <!-- Dangerous permission witch should be treated carefully. -->

    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="android.permission.SEND_SMS" />
    <uses-permission android:name="android.permission.RECEIVE_SMS" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.READ_CALENDAR" />
    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.SET_WALLPAPER" />
    <uses-permission android:name="android.permission.SET_WALLPAPER_HINTS" />
    <uses-permission
        android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.MOUNT_FORMAT_FILESYSTEMS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.NFC" />
    <uses-permission android:name="moe.shizuku.manager.permission.API_V23" />
    <uses-permission android:name="com.android.vending.BILLING" />

    <!-- Property "largeHeap" is not recommended to set to true. -->
    <!-- https://stackoverflow.com/questions/27396892/what-are-advantages-of-setting-largeheap-to-true -->
    <!-- However, "largeHeap" may be helpful for prevent LeakCanary from OOM when analyzing detected memory leaks. -->
    <application
        android:name="org.autojs.autojs.App"
        android:allowBackup="false"
        android:hardwareAccelerated="true"
        android:icon="${icon}"
        android:label="${appName}"
        android:largeHeap="true"
        android:localeConfig="@xml/locales_config"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:ignore="DataExtractionRules"
        tools:targetApi="tiramisu" >
        <meta-data
            android:name="android.max_aspect"
            android:value="2.1" />
        <meta-data
            android:name="CHANNEL"
            android:value="${CHANNEL}" />

        <activity
            android:name="org.autojs.autojs.ui.splash.SplashActivity"
            android:exported="true"
            android:icon="@drawable/autojs6_material"
            android:label="@string/app_name"
            android:theme="@style/AppTheme.Splash"
            tools:ignore="RedundantLabel" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="${intentCategory}" />
            </intent-filter>

            <meta-data
                android:name="android.app.shortcuts"
                android:resource="@xml/app_shortcuts" />
        </activity>
        <activity
            android:name="org.autojs.autojs.ui.main.MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/AppTheme.FullScreen" />
        <activity
            android:name="org.autojs.autojs.ui.main.scripts.DisplayManifestActivity"
            android:theme="@style/AppTheme.Settings" />
        <activity
            android:name="org.autojs.autojs.ui.main.scripts.DisplayMediaInfoActivity"
            android:theme="@style/AppTheme.Settings" />
        <activity
            android:name="org.autojs.autojs.ui.settings.DisplayVersionHistoriesActivity"
            android:theme="@style/AppTheme.Settings" />
        <activity
            android:name="org.autojs.autojs.external.shortcut.ShortcutActivity"
            android:exported="true"
            android:taskAffinity="org.autojs.autojs.external.shortcut.ShortcutActivity"
            android:theme="@android:style/Theme.NoDisplay" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${authorities}"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>

        <activity
            android:name="org.autojs.autojs.ui.edit.EditActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|locale"
            android:launchMode="standard"
            android:multiprocess="true"
            android:taskAffinity="org.autojs.autojs.edit"
            android:theme="@style/EditorTheme" />
        <activity
            android:name="org.autojs.autojs.ui.settings.AboutActivity"
            android:theme="@style/AppTheme.Settings" />
        <activity
            android:name="org.autojs.autojs.ui.settings.PreferencesActivity"
            android:exported="true"
            android:label="@string/text_app_shortcut_settings_long_label"
            android:theme="@style/AppTheme.Settings" />
        <activity
            android:name="org.autojs.autojs.ui.settings.DeveloperOptionsActivity"
            android:theme="@style/AppTheme.Settings" />

        <!-- <activity android:name="org.autojs.autojs.ui.error.ErrorReportActivity" android:process=":crash_report"/> -->
        <activity android:name="org.autojs.autojs.ui.error.ErrorReportActivity" />
        <activity
            android:name="org.autojs.autojs.external.tasker.TaskerScriptEditActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|locale" />
        <activity
            android:name="org.autojs.autojs.ui.project.BuildActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|locale" />
        <activity
            android:name="org.autojs.autojs.ui.error.ErrorDialogActivity"
            android:exported="false"
            android:theme="@style/AppTheme.Transparent" />
        <activity android:name="org.autojs.autojs.ui.project.ProjectConfigActivity" />
        <activity android:name="org.autojs.autojs.ui.keystore.ManageKeyStoreActivity" />
        <activity
            android:name="org.autojs.autojs.ui.log.LogActivity"
            android:exported="true"
            android:label="@string/text_app_shortcut_log_long_label"
            android:launchMode="singleTop"
            android:taskAffinity="org.autojs.autojs.ui.log.LogActivity"
            android:theme="@style/AppTheme.FullScreen" />
        <activity
            android:name="org.autojs.autojs.ui.doc.DocumentationActivity"
            android:exported="true"
            android:label="@string/text_app_shortcut_docs_long_label"
            android:launchMode="singleTop"
            android:taskAffinity="org.autojs.autojs.ui.doc.DocumentationActivity"
            android:theme="@style/AppTheme.FullScreen" />
        <activity
            android:name="org.autojs.autojs.theme.app.ColorSearchHelpActivity"
            android:theme="@style/AppTheme.FullScreen" />
        <activity android:name="org.autojs.autojs.ui.shortcut.AppsIconSelectActivity" />
        <activity android:name="org.autojs.autojs.ui.timing.TimedTaskSettingActivity" />
        <activity
            android:name="org.autojs.autojs.ui.shortcut.ShortcutCreateActivity"
            android:theme="@style/AppTheme.Transparent" />
        <activity
            android:name="org.autojs.autojs.external.widget.ScriptWidgetSettingsActivity"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_CONFIGURE" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.CREATE_SHORTCUT" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <service android:name="org.autojs.autojs.ui.enhancedfloaty.FloatyService" />
        <service
            android:name="org.autojs.autojs.external.foreground.MainActivityForegroundService"
            android:foregroundServiceType="specialUse" />
        <service android:name="org.autojs.autojs.external.ScriptExecutionIntentService" />

        <activity android:name="org.autojs.autojs.external.tasker.TaskPrefEditActivity" />

        <activity-alias
            android:name="org.autojs.autojs.external.tasker.PluginActivity"
            android:exported="true"
            android:icon="@drawable/autojs6_material"
            android:label="@string/app_name"
            android:targetActivity="org.autojs.autojs.external.tasker.TaskPrefEditActivity"
            tools:ignore="ExportedActivity" >
            <intent-filter>
                <action android:name="com.twofortyfouram.locale.intent.action.EDIT_SETTING" />
            </intent-filter>
        </activity-alias>

        <service
            android:name="org.autojs.autojs.external.tile.LayoutBoundsTile"
            android:exported="true"
            android:icon="@drawable/ic_circular_menu_bounds"
            android:label="@string/text_inspect_layout_bounds"
            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE" >
            <meta-data
                android:name="android.service.quicksettings.ACTIVE_TILE"
                android:value="false" />

            <intent-filter>
                <action android:name="android.service.quicksettings.action.QS_TILE" />
            </intent-filter>
        </service>
        <service
            android:name="org.autojs.autojs.external.tile.LayoutHierarchyTile"
            android:exported="true"
            android:icon="@drawable/ic_circular_menu_hierarchy"
            android:label="@string/text_inspect_layout_hierarchy"
            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE" >
            <meta-data
                android:name="android.service.quicksettings.ACTIVE_TILE"
                android:value="false" />

            <intent-filter>
                <action android:name="android.service.quicksettings.action.QS_TILE" />
            </intent-filter>
        </service>

        <receiver
            android:name="org.autojs.autojs.external.receiver.StaticBroadcastReceiver"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="android.intent.action.TIME_SET" />
                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
                <action android:name="android.intent.action.UID_REMOVED" />
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
                <action android:name="android.intent.action.ACTION_SHUTDOWN" />
                <action android:name="android.intent.action.ACTION_BATTERY_CHANGED" />
                <action android:name="android.intent.action.DATE_CHANGED" />
                <action android:name="android.intent.action.DREAMING_STARTED" />
                <action android:name="android.intent.action.DREAMING_STOPPED" />
                <action android:name="android.intent.action.HEADSET_PLUG" />
                <action android:name="android.intent.action.INPUT_METHOD_CHANGED" />
                <action android:name="android.intent.action.LOCALE_CHANGED" />
                <action android:name="android.intent.action.MEDIA_BUTTON" />
                <action android:name="android.intent.action.MEDIA_CHECKING" />
                <action android:name="android.intent.action.MEDIA_MOUNTED" />
                <action android:name="android.intent.action.PACKAGE_FIRST_LAUNCH" />
                <action android:name="android.intent.action.PROVIDER_CHANGED" />
                <action android:name="android.intent.action.WALLPAPER_CHANGED" />
                <action android:name="android.intent.action.USER_UNLOCKED" />
                <action android:name="android.intent.action.USER_PRESENT" />
                <action
                    android:name="android.net.conn.CONNECTIVITY_CHANGE"
                    tools:ignore="BatteryLife" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.PACKAGE_ADDED" />
                <action android:name="android.intent.action.PACKAGE_CHANGED" />
                <action android:name="android.intent.action.PACKAGE_DATA_CLEARED" />
                <action android:name="android.intent.action.PACKAGE_REMOVED" />
                <action android:name="android.intent.action.PACKAGE_RESTARTED" />

                <data android:scheme="package" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="org.autojs.autojs.external.tasker.FireSettingReceiver"
            android:exported="true"
            android:process=":background"
            tools:ignore="ExportedReceiver" >

            <!-- this Intent filter allows the plug-in to be discovered by the host. -->

            <intent-filter>
                <action android:name="com.twofortyfouram.locale.intent.action.FIRE_SETTING" />
            </intent-filter>
        </receiver>

        <activity
            android:name="org.autojs.autojs.external.open.EditIntentActivity"
            android:exported="true"
            android:icon="@drawable/autojs6_material"
            android:label="@string/text_edit_script" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <action android:name="android.intent.action.EDIT" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="file" />
                <data android:scheme="content" />
                <data android:mimeType="application/x-javascript" />
                <data android:mimeType="text/plain" />
            </intent-filter>
        </activity>
        <activity
            android:name="org.autojs.autojs.external.open.RunIntentActivity"
            android:exported="true"
            android:icon="@drawable/autojs6_material"
            android:label="@string/text_run_script"
            android:taskAffinity="org.autojs.autojs.external.open.RunIntentActivity"
            android:theme="@android:style/Theme.NoDisplay" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <action android:name="android.intent.action.EDIT" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="file" />
                <data android:scheme="content" />
                <data android:mimeType="application/x-javascript" />
                <data android:mimeType="text/plain" />
            </intent-filter>
        </activity>
        <activity
            android:name="org.autojs.autojs.external.open.ImportIntentActivity"
            android:exported="true"
            android:icon="@drawable/autojs6_material"
            android:label="@string/text_import_script"
            android:theme="@style/AppTheme.Transparent" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <action android:name="android.intent.action.EDIT" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="file" />
                <data android:scheme="content" />
                <data android:mimeType="application/x-javascript" />
                <data android:mimeType="text/plain" />
            </intent-filter>
        </activity>

        <service
            android:name="org.autojs.autojs.core.notification.NotificationListenerService"
            android:exported="false"
            android:label="@string/app_name"
            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
            <intent-filter>
                <action android:name="android.service.notification.NotificationListenerService" />
            </intent-filter>
        </service>

        <receiver
            android:name="org.autojs.autojs.external.widget.ScriptWidget"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/script_widget_config" />
        </receiver>
        <receiver
            android:name="org.autojs.autojs.timing.TaskReceiver"
            android:exported="false" >
            <intent-filter>
                <action android:name="org.autojs.autojs.action.task" />
            </intent-filter>
        </receiver>

        <!-- <receiver -->
        <!-- android:name="org.autojs.autojs.ui.main.drawer.DrawerFragment.BatteryChangedReceiver" -->
        <!-- android:exported="true"> -->
        <!-- <intent-filter> -->
        <!-- <action android:name="android.intent.action.ACTION_BATTERY_CHANGED" /> -->
        <!-- <action android:name="android.intent.action.ACTION_POWER_CONNECTED" /> -->
        <!-- <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" /> -->
        <!-- </intent-filter> -->
        <!-- </receiver> -->

        <activity
            android:name="org.autojs.autojs.theme.app.ColorSelectActivity"
            android:theme="@style/MtAppTheme.FullScreen" />
        <activity
            android:name="org.autojs.autojs.theme.app.ColorLibrariesActivity"
            android:theme="@style/MtAppTheme.FullScreen" />
        <activity
            android:name="org.autojs.autojs.theme.app.ColorItemsActivity"
            android:theme="@style/MtAppTheme.FullScreen" />
        <activity
            android:name="org.autojs.autojs.execution.ScriptExecuteActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|locale"
            android:theme="@style/ScriptTheme" />
        <activity
            android:name="org.autojs.autojs.core.permission.PermissionRequestActivity"
            android:excludeFromRecents="true"
            android:taskAffinity="org.autojs.autojs.core.permission.PermissionRequestActivity"
            android:theme="@style/ScriptTheme.Transparent" />
        <activity
            android:name="org.autojs.autojs.core.activity.StartForResultActivity"
            android:excludeFromRecents="true"
            android:taskAffinity="org.autojs.autojs.core.activity.StartForResultActivity"
            android:theme="@style/ScriptTheme.Transparent" />

        <service
            android:name="org.autojs.autojs.core.accessibility.AccessibilityServiceUsher"
            android:exported="true"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>

            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility_service_config" />
        </service>
        <service
            android:name="org.autojs.autojs.core.image.capture.ScreenCapturerForegroundService"
            android:foregroundServiceType="mediaProjection" />
        <service
            android:name="androidx.appcompat.app.AppLocalesMetadataHolderService"
            android:enabled="false"
            android:exported="false" >
            <meta-data
                android:name="autoStoreLocales"
                android:value="true" />
        </service>
        <service android:name="org.eclipse.paho.android.service.MqttService" />

        <activity
            android:name="org.autojs.autojs.inrt.SplashActivity"
            android:exported="true"
            android:theme="@style/AppTheme.Splash" >
            <intent-filter tools:ignore="ExtraText" >
                <action android:name="android.intent.action.MAIN" />

                <category android:name="${intentCategoryInrt}" />
            </intent-filter>
        </activity>
        <activity
            android:name="org.autojs.autojs.inrt.LogActivity"
            android:theme="@style/AppTheme.NoActionBarInrt" />
        <activity
            android:name="org.autojs.autojs.inrt.SettingsActivity"
            android:theme="@style/AppTheme.NoActionBarInrt" />

        <!-- This provider is required by Shizuku, remove this if your app only supports Sui -->
        <provider
            android:name="rikka.shizuku.ShizukuProvider"
            android:authorities="${applicationId}.shizuku"
            android:enabled="true"
            android:exported="true"
            android:multiprocess="false"
            android:permission="android.permission.INTERACT_ACROSS_USERS_FULL" />

        <meta-data
            android:name="moe.shizuku.client.V3_SUPPORT"
            android:value="true" />
    </application>

</manifest>