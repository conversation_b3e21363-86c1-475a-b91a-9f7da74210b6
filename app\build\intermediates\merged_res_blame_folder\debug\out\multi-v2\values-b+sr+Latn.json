{"logs": [{"outputFile": "com.bm.atool.app-mergeDebugResources-43:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bca5d58794b22aca6036379432e392dd\\transformed\\navigation-ui-2.7.7\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,162", "endColumns": "106,122", "endOffsets": "157,280"}, "to": {"startLines": "96,97", "startColumns": "4,4", "startOffsets": "8211,8318", "endColumns": "106,122", "endOffsets": "8313,8436"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\cee19163c10f1fafd81d90a045427d43\\transformed\\core-1.13.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "35,36,37,38,39,40,41,102", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3140,3238,3340,3437,3541,3645,3750,8840", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "3233,3335,3432,3536,3640,3745,3861,8936"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\29973079252e5e41b13d785bb69b8fcd\\transformed\\material-1.5.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,279,359,453,585,666,732,825,893,956,1059,1125,1181,1252,1312,1366,1478,1535,1596,1650,1726,1851,1938,2021,2130,2212,2295,2383,2450,2516,2590,2668,2757,2833,2909,2984,3056,3146,3219,3311,3407,3479,3555,3651,3704,3771,3858,3945,4007,4071,4134,4239,4343,4439,4546", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,79,93,131,80,65,92,67,62,102,65,55,70,59,53,111,56,60,53,75,124,86,82,108,81,82,87,66,65,73,77,88,75,75,74,71,89,72,91,95,71,75,95,52,66,86,86,61,63,62,104,103,95,106,79", "endOffsets": "274,354,448,580,661,727,820,888,951,1054,1120,1176,1247,1307,1361,1473,1530,1591,1645,1721,1846,1933,2016,2125,2207,2290,2378,2445,2511,2585,2663,2752,2828,2904,2979,3051,3141,3214,3306,3402,3474,3550,3646,3699,3766,3853,3940,4002,4066,4129,4234,4338,4434,4541,4621"}, "to": {"startLines": "2,34,42,43,44,46,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3060,3866,3960,4092,4244,4397,4490,4558,4621,4724,4790,4846,4917,4977,5031,5143,5200,5261,5315,5391,5516,5603,5686,5795,5877,5960,6048,6115,6181,6255,6333,6422,6498,6574,6649,6721,6811,6884,6976,7072,7144,7220,7316,7369,7436,7523,7610,7672,7736,7799,7904,8008,8104,8521", "endLines": "6,34,42,43,44,46,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99", "endColumns": "12,79,93,131,80,65,92,67,62,102,65,55,70,59,53,111,56,60,53,75,124,86,82,108,81,82,87,66,65,73,77,88,75,75,74,71,89,72,91,95,71,75,95,52,66,86,86,61,63,62,104,103,95,106,79", "endOffsets": "324,3135,3955,4087,4168,4305,4485,4553,4616,4719,4785,4841,4912,4972,5026,5138,5195,5256,5310,5386,5511,5598,5681,5790,5872,5955,6043,6110,6176,6250,6328,6417,6493,6569,6644,6716,6806,6879,6971,7067,7139,7215,7311,7364,7431,7518,7605,7667,7731,7794,7899,8003,8099,8206,8596"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\466bd753c0bc663f243f57f85dfa0cbd\\transformed\\preference-1.2.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,343,495,664,751", "endColumns": "70,86,79,151,168,86,82", "endOffsets": "171,258,338,490,659,746,829"}, "to": {"startLines": "45,47,98,100,103,104,105", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4173,4310,8441,8601,8941,9110,9197", "endColumns": "70,86,79,151,168,86,82", "endOffsets": "4239,4392,8516,8748,9105,9192,9275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4b1b6d71ac725a2fc4ab91a425e45a1c\\transformed\\appcompat-1.7.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,2918"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "329,436,537,643,729,833,955,1040,1122,1213,1306,1401,1495,1595,1688,1783,1888,1979,2070,2156,2261,2367,2470,2577,2686,2793,2963,8753", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "431,532,638,724,828,950,1035,1117,1208,1301,1396,1490,1590,1683,1778,1883,1974,2065,2151,2256,2362,2465,2572,2681,2788,2958,3055,8835"}}]}]}