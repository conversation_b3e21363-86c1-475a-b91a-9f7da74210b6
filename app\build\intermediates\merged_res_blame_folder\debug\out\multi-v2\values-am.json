{"logs": [{"outputFile": "com.bm.atool.app-mergeDebugResources-43:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\cee19163c10f1fafd81d90a045427d43\\transformed\\core-1.13.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "34,35,36,37,38,39,40,101", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2959,3052,3152,3249,3348,3444,3546,8232", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "3047,3147,3244,3343,3439,3541,3641,8328"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bca5d58794b22aca6036379432e392dd\\transformed\\navigation-ui-2.7.7\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,159", "endColumns": "103,109", "endOffsets": "154,264"}, "to": {"startLines": "95,96", "startColumns": "4,4", "startOffsets": "7657,7761", "endColumns": "103,109", "endOffsets": "7756,7866"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\29973079252e5e41b13d785bb69b8fcd\\transformed\\material-1.5.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,216,291,380,482,559,623,708,770,828,913,975,1033,1099,1161,1216,1312,1369,1428,1484,1551,1656,1736,1817,1916,1989,2060,2142,2209,2275,2341,2414,2495,2563,2636,2707,2774,2859,2926,3013,3101,3175,3243,3328,3379,3443,3523,3605,3667,3731,3794,3889,3978,4063,4154", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,74,88,101,76,63,84,61,57,84,61,57,65,61,54,95,56,58,55,66,104,79,80,98,72,70,81,66,65,65,72,80,67,72,70,66,84,66,86,87,73,67,84,50,63,79,81,61,63,62,94,88,84,90,75", "endOffsets": "211,286,375,477,554,618,703,765,823,908,970,1028,1094,1156,1211,1307,1364,1423,1479,1546,1651,1731,1812,1911,1984,2055,2137,2204,2270,2336,2409,2490,2558,2631,2702,2769,2854,2921,3008,3096,3170,3238,3323,3374,3438,3518,3600,3662,3726,3789,3884,3973,4058,4149,4225"}, "to": {"startLines": "2,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2884,3646,3735,3837,3980,4126,4211,4273,4331,4416,4478,4536,4602,4664,4719,4815,4872,4931,4987,5054,5159,5239,5320,5419,5492,5563,5645,5712,5778,5844,5917,5998,6066,6139,6210,6277,6362,6429,6516,6604,6678,6746,6831,6882,6946,7026,7108,7170,7234,7297,7392,7481,7566,7945", "endLines": "5,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "endColumns": "12,74,88,101,76,63,84,61,57,84,61,57,65,61,54,95,56,58,55,66,104,79,80,98,72,70,81,66,65,65,72,80,67,72,70,66,84,66,86,87,73,67,84,50,63,79,81,61,63,62,94,88,84,90,75", "endOffsets": "261,2954,3730,3832,3909,4039,4206,4268,4326,4411,4473,4531,4597,4659,4714,4810,4867,4926,4982,5049,5154,5234,5315,5414,5487,5558,5640,5707,5773,5839,5912,5993,6061,6134,6205,6272,6357,6424,6511,6599,6673,6741,6826,6877,6941,7021,7103,7165,7229,7292,7387,7476,7561,7652,8016"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4b1b6d71ac725a2fc4ab91a425e45a1c\\transformed\\appcompat-1.7.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "266,364,462,568,654,757,874,952,1028,1119,1212,1304,1398,1498,1591,1686,1779,1870,1961,2041,2141,2241,2337,2439,2539,2638,2788,8152", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "359,457,563,649,752,869,947,1023,1114,1207,1299,1393,1493,1586,1681,1774,1865,1956,2036,2136,2236,2332,2434,2534,2633,2783,2879,8227"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\466bd753c0bc663f243f57f85dfa0cbd\\transformed\\preference-1.2.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,253,327,458,627,708", "endColumns": "65,81,73,130,168,80,77", "endOffsets": "166,248,322,453,622,703,781"}, "to": {"startLines": "44,46,97,99,102,103,104", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3914,4044,7871,8021,8333,8502,8583", "endColumns": "65,81,73,130,168,80,77", "endOffsets": "3975,4121,7940,8147,8497,8578,8656"}}]}]}