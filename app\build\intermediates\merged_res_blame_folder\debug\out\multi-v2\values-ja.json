{"logs": [{"outputFile": "com.bm.atool.app-mergeDebugResources-43:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\29973079252e5e41b13d785bb69b8fcd\\transformed\\material-1.5.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,208,277,362,466,542,605,689,753,811,892,956,1011,1070,1127,1181,1274,1330,1387,1441,1507,1607,1683,1764,1856,1918,1980,2059,2126,2192,2262,2332,2409,2473,2544,2612,2675,2754,2817,2897,2979,3051,3122,3194,3242,3306,3381,3458,3520,3584,3647,3733,3817,3898,3983", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,68,84,103,75,62,83,63,57,80,63,54,58,56,53,92,55,56,53,65,99,75,80,91,61,61,78,66,65,69,69,76,63,70,67,62,78,62,79,81,71,70,71,47,63,74,76,61,63,62,85,83,80,84,72", "endOffsets": "203,272,357,461,537,600,684,748,806,887,951,1006,1065,1122,1176,1269,1325,1382,1436,1502,1602,1678,1759,1851,1913,1975,2054,2121,2187,2257,2327,2404,2468,2539,2607,2670,2749,2812,2892,2974,3046,3117,3189,3237,3301,3376,3453,3515,3579,3642,3728,3812,3893,3978,4051"}, "to": {"startLines": "2,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2844,3582,3667,3771,3914,4060,4144,4208,4266,4347,4411,4466,4525,4582,4636,4729,4785,4842,4896,4962,5062,5138,5219,5311,5373,5435,5514,5581,5647,5717,5787,5864,5928,5999,6067,6130,6209,6272,6352,6434,6506,6577,6649,6697,6761,6836,6913,6975,7039,7102,7188,7272,7353,7717", "endLines": "5,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "endColumns": "12,68,84,103,75,62,83,63,57,80,63,54,58,56,53,92,55,56,53,65,99,75,80,91,61,61,78,66,65,69,69,76,63,70,67,62,78,62,79,81,71,70,71,47,63,74,76,61,63,62,85,83,80,84,72", "endOffsets": "253,2908,3662,3766,3842,3972,4139,4203,4261,4342,4406,4461,4520,4577,4631,4724,4780,4837,4891,4957,5057,5133,5214,5306,5368,5430,5509,5576,5642,5712,5782,5859,5923,5994,6062,6125,6204,6267,6347,6429,6501,6572,6644,6692,6756,6831,6908,6970,7034,7097,7183,7267,7348,7433,7785"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bca5d58794b22aca6036379432e392dd\\transformed\\navigation-ui-2.7.7\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,154", "endColumns": "98,109", "endOffsets": "149,259"}, "to": {"startLines": "95,96", "startColumns": "4,4", "startOffsets": "7438,7537", "endColumns": "98,109", "endOffsets": "7532,7642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4b1b6d71ac725a2fc4ab91a425e45a1c\\transformed\\appcompat-1.7.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "258,355,448,553,635,733,841,919,994,1085,1178,1273,1367,1467,1560,1655,1749,1840,1931,2009,2111,2209,2304,2407,2503,2599,2747,7918", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "350,443,548,630,728,836,914,989,1080,1173,1268,1362,1462,1555,1650,1744,1835,1926,2004,2106,2204,2299,2402,2498,2594,2742,2839,7992"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\cee19163c10f1fafd81d90a045427d43\\transformed\\core-1.13.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "34,35,36,37,38,39,40,101", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2913,3005,3105,3199,3295,3388,3481,7997", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "3000,3100,3194,3290,3383,3476,3577,8093"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\466bd753c0bc663f243f57f85dfa0cbd\\transformed\\preference-1.2.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,172,255,325,453,621,701", "endColumns": "66,82,69,127,167,79,75", "endOffsets": "167,250,320,448,616,696,772"}, "to": {"startLines": "44,46,97,99,102,103,104", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3847,3977,7647,7790,8098,8266,8346", "endColumns": "66,82,69,127,167,79,75", "endOffsets": "3909,4055,7712,7913,8261,8341,8417"}}]}]}