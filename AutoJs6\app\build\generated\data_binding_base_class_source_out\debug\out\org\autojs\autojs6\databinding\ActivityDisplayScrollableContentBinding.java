// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.HorizontalScrollView;
import android.widget.RelativeLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.ui.common.NestedOuterScrollView;
import org.autojs.autojs.ui.widget.SelectableTextView;
import org.autojs.autojs6.R;

public final class ActivityDisplayScrollableContentBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final FloatingActionButton fab;

  @NonNull
  public final HorizontalScrollView innerScrollView;

  @NonNull
  public final NestedOuterScrollView outerScrollView;

  @NonNull
  public final SelectableTextView textView;

  private ActivityDisplayScrollableContentBinding(@NonNull RelativeLayout rootView,
      @NonNull FloatingActionButton fab, @NonNull HorizontalScrollView innerScrollView,
      @NonNull NestedOuterScrollView outerScrollView, @NonNull SelectableTextView textView) {
    this.rootView = rootView;
    this.fab = fab;
    this.innerScrollView = innerScrollView;
    this.outerScrollView = outerScrollView;
    this.textView = textView;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityDisplayScrollableContentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityDisplayScrollableContentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_display_scrollable_content, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityDisplayScrollableContentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.fab;
      FloatingActionButton fab = ViewBindings.findChildViewById(rootView, id);
      if (fab == null) {
        break missingId;
      }

      id = R.id.innerScrollView;
      HorizontalScrollView innerScrollView = ViewBindings.findChildViewById(rootView, id);
      if (innerScrollView == null) {
        break missingId;
      }

      id = R.id.outerScrollView;
      NestedOuterScrollView outerScrollView = ViewBindings.findChildViewById(rootView, id);
      if (outerScrollView == null) {
        break missingId;
      }

      id = R.id.textView;
      SelectableTextView textView = ViewBindings.findChildViewById(rootView, id);
      if (textView == null) {
        break missingId;
      }

      return new ActivityDisplayScrollableContentBinding((RelativeLayout) rootView, fab,
          innerScrollView, outerScrollView, textView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
