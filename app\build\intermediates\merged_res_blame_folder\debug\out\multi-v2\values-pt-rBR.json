{"logs": [{"outputFile": "com.bm.atool.app-mergeDebugResources-43:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\29973079252e5e41b13d785bb69b8fcd\\transformed\\material-1.5.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,312,413,533,614,678,770,849,909,999,1070,1133,1208,1272,1326,1453,1511,1573,1627,1706,1847,1934,2016,2125,2208,2292,2379,2446,2512,2586,2666,2753,2826,2903,2972,3046,3134,3211,3304,3400,3474,3554,3651,3703,3769,3856,3944,4006,4070,4133,4245,4354,4461,4571", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,83,100,119,80,63,91,78,59,89,70,62,74,63,53,126,57,61,53,78,140,86,81,108,82,83,86,66,65,73,79,86,72,76,68,73,87,76,92,95,73,79,96,51,65,86,87,61,63,62,111,108,106,109,76", "endOffsets": "223,307,408,528,609,673,765,844,904,994,1065,1128,1203,1267,1321,1448,1506,1568,1622,1701,1842,1929,2011,2120,2203,2287,2374,2441,2507,2581,2661,2748,2821,2898,2967,3041,3129,3206,3299,3395,3469,3549,3646,3698,3764,3851,3939,4001,4065,4128,4240,4349,4456,4566,4643"}, "to": {"startLines": "2,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3016,3835,3936,4056,4207,4358,4450,4529,4589,4679,4750,4813,4888,4952,5006,5133,5191,5253,5307,5386,5527,5614,5696,5805,5888,5972,6059,6126,6192,6266,6346,6433,6506,6583,6652,6726,6814,6891,6984,7080,7154,7234,7331,7383,7449,7536,7624,7686,7750,7813,7925,8034,8141,8562", "endLines": "5,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "endColumns": "12,83,100,119,80,63,91,78,59,89,70,62,74,63,53,126,57,61,53,78,140,86,81,108,82,83,86,66,65,73,79,86,72,76,68,73,87,76,92,95,73,79,96,51,65,86,87,61,63,62,111,108,106,109,76", "endOffsets": "273,3095,3931,4051,4132,4266,4445,4524,4584,4674,4745,4808,4883,4947,5001,5128,5186,5248,5302,5381,5522,5609,5691,5800,5883,5967,6054,6121,6187,6261,6341,6428,6501,6578,6647,6721,6809,6886,6979,7075,7149,7229,7326,7378,7444,7531,7619,7681,7745,7808,7920,8029,8136,8246,8634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\cee19163c10f1fafd81d90a045427d43\\transformed\\core-1.13.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "34,35,36,37,38,39,40,101", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3100,3197,3299,3398,3498,3605,3715,8876", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3192,3294,3393,3493,3600,3710,3830,8972"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\466bd753c0bc663f243f57f85dfa0cbd\\transformed\\preference-1.2.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,341,492,661,748", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "170,257,336,487,656,743,824"}, "to": {"startLines": "44,46,97,99,102,103,104", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4137,4271,8483,8639,8977,9146,9233", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "4202,4353,8557,8785,9141,9228,9309"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bca5d58794b22aca6036379432e392dd\\transformed\\navigation-ui-2.7.7\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "95,96", "startColumns": "4,4", "startOffsets": "8251,8363", "endColumns": "111,119", "endOffsets": "8358,8478"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4b1b6d71ac725a2fc4ab91a425e45a1c\\transformed\\appcompat-1.7.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "278,398,504,611,700,801,920,1005,1085,1176,1269,1364,1458,1558,1651,1746,1841,1932,2023,2108,2215,2326,2428,2536,2644,2754,2916,8790", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "393,499,606,695,796,915,1000,1080,1171,1264,1359,1453,1553,1646,1741,1836,1927,2018,2103,2210,2321,2423,2531,2639,2749,2911,3011,8871"}}]}]}