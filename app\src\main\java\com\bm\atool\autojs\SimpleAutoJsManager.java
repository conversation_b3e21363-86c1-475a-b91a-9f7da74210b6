package com.bm.atool.autojs;

import android.app.Application;
import android.content.Context;
import android.util.Log;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 增强的SimpleAutoJsManager
 * 提供完整的AutoJs6兼容API，基于WebView + 真实UI操作
 * 避免复杂的AutoJs6直接集成问题
 */
public class SimpleAutoJsManager {
    private static final String TAG = "SimpleAutoJsManager";
    
    private static SimpleAutoJsManager instance;
    private Context context;
    private boolean isInitialized = false;
    private ConcurrentHashMap<String, SimpleJsEngine> runningScripts;
    private AtomicInteger scriptCounter;

    private SimpleAutoJsManager() {
        runningScripts = new ConcurrentHashMap<>();
        scriptCounter = new AtomicInteger(0);
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized SimpleAutoJsManager getInstance() {
        if (instance == null) {
            instance = new SimpleAutoJsManager();
        }
        return instance;
    }
    
    /**
     * 初始化管理器
     */
    public boolean initialize(Application application) {
        if (isInitialized) {
            Log.d(TAG, "SimpleAutoJsManager already initialized");
            return true;
        }

        try {
            this.context = application.getApplicationContext();
            isInitialized = true;
            Log.i(TAG, "=== SimpleAutoJsManager 初始化成功 ===");
            Log.d(TAG, "提供完整的AutoJs6兼容API支持");
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize SimpleAutoJsManager", e);
            return false;
        }
    }
    
    /**
     * 检查是否已初始化
     */
    public boolean isInitialized() {
        return isInitialized;
    }
    
    /**
     * 脚本执行回调接口
     */
    public interface ScriptExecutionCallback {
        void onStart(String scriptName);
        void onSuccess(String scriptName, String result);
        void onError(String scriptName, String error);
    }
    
    /**
     * 执行JavaScript脚本 - AutoJs6兼容版本
     */
    public String executeScript(String scriptContent, String scriptName, ScriptExecutionCallback callback) {
        Log.i(TAG, "=== 执行AutoJs6兼容脚本 ===");
        Log.d(TAG, "Script name: " + scriptName);
        Log.d(TAG, "Script content length: " + scriptContent.length());
        
        if (!isInitialized()) {
            Log.e(TAG, "SimpleAutoJsManager not initialized");
            if (callback != null) {
                callback.onError(scriptName, "SimpleAutoJsManager not initialized");
            }
            return null;
        }

        try {
            // 生成唯一的脚本ID
            String scriptId = scriptName + "_" + scriptCounter.incrementAndGet();
            Log.d(TAG, "Generated script ID: " + scriptId);

            // 创建增强的JavaScript引擎，支持完整的AutoJs6 API
            Log.d(TAG, "Creating enhanced SimpleJsEngine with AutoJs6 API support...");
            SimpleJsEngine engine = new SimpleJsEngine(context);
            
            // 执行脚本
            engine.executeScript(scriptContent, new SimpleJsEngine.ScriptCallback() {
                @Override
                public void onStart() {
                    Log.i(TAG, "=== AutoJs6兼容脚本开始执行 ===");
                    if (callback != null) {
                        callback.onStart(scriptName);
                    }
                }

                @Override
                public void onSuccess(String result) {
                    Log.i(TAG, "=== AutoJs6兼容脚本执行成功 ===");
                    Log.d(TAG, "Result: " + result);
                    runningScripts.remove(scriptId);
                    if (callback != null) {
                        callback.onSuccess(scriptName, result);
                    }
                }

                @Override
                public void onError(String error) {
                    Log.e(TAG, "=== AutoJs6兼容脚本执行失败 ===");
                    Log.e(TAG, "Error: " + error);
                    runningScripts.remove(scriptId);
                    if (callback != null) {
                        callback.onError(scriptName, error);
                    }
                }
            });

            runningScripts.put(scriptId, engine);
            Log.i(TAG, "=== AutoJs6兼容脚本提交成功 ===");
            Log.d(TAG, "Running scripts count: " + runningScripts.size());
            return scriptId;

        } catch (Exception e) {
            Log.e(TAG, "=== AutoJs6兼容脚本执行异常 ===");
            Log.e(TAG, "Failed to execute script: " + scriptName, e);
            if (callback != null) {
                callback.onError(scriptName, e.getMessage());
            }
            return null;
        }
    }
    
    /**
     * 停止指定脚本
     */
    public boolean stopScript(String scriptId) {
        Log.d(TAG, "Stopping AutoJs6 compatible script: " + scriptId);
        
        SimpleJsEngine engine = runningScripts.get(scriptId);
        if (engine != null) {
            try {
                engine.stopScript();
                runningScripts.remove(scriptId);
                Log.i(TAG, "AutoJs6 compatible script stopped successfully: " + scriptId);
                return true;
            } catch (Exception e) {
                Log.e(TAG, "Failed to stop AutoJs6 compatible script: " + scriptId, e);
                return false;
            }
        } else {
            Log.w(TAG, "AutoJs6 compatible script not found: " + scriptId);
            return false;
        }
    }
    
    /**
     * 停止所有脚本
     */
    public int stopAllScripts() {
        Log.d(TAG, "Stopping all AutoJs6 compatible scripts...");
        
        try {
            int stoppedCount = runningScripts.size();
            for (SimpleJsEngine engine : runningScripts.values()) {
                engine.stopScript();
            }
            runningScripts.clear();
            Log.i(TAG, "Stopped " + stoppedCount + " AutoJs6 compatible scripts successfully");
            return stoppedCount;
        } catch (Exception e) {
            Log.e(TAG, "Failed to stop all AutoJs6 compatible scripts", e);
            return 0;
        }
    }
    
    /**
     * 检查无障碍服务是否启用
     */
    public boolean isAccessibilityServiceEnabled() {
        // 这里会在SimpleJsEngine中实现真实的无障碍服务检查
        return true; // 临时返回true，实际实现在SimpleJsEngine中
    }
    
    /**
     * 获取运行中的脚本数量
     */
    public int getRunningScriptCount() {
        return runningScripts.size();
    }
    
    /**
     * 获取运行中的脚本ID列表
     */
    public String[] getRunningScriptIds() {
        return runningScripts.keySet().toArray(new String[0]);
    }
}
