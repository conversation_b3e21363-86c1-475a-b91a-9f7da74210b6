{"logs": [{"outputFile": "com.bm.atool.app-mergeDebugResources-43:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\cee19163c10f1fafd81d90a045427d43\\transformed\\core-1.13.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "34,35,36,37,38,39,40,101", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3054,3153,3255,3353,3450,3558,3669,8810", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "3148,3250,3348,3445,3553,3664,3786,8906"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4b1b6d71ac725a2fc4ab91a425e45a1c\\transformed\\appcompat-1.7.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "272,386,486,598,684,790,913,995,1073,1164,1257,1352,1446,1547,1640,1735,1832,1923,2016,2097,2203,2307,2405,2511,2615,2717,2871,8728", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "381,481,593,679,785,908,990,1068,1159,1252,1347,1441,1542,1635,1730,1827,1918,2011,2092,2198,2302,2400,2506,2610,2712,2866,2963,8805"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\466bd753c0bc663f243f57f85dfa0cbd\\transformed\\preference-1.2.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,266,350,498,667,751", "endColumns": "68,91,83,147,168,83,78", "endOffsets": "169,261,345,493,662,746,825"}, "to": {"startLines": "44,46,97,99,102,103,104", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4101,4235,8413,8580,8911,9080,9164", "endColumns": "68,91,83,147,168,83,78", "endOffsets": "4165,4322,8492,8723,9075,9159,9238"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\29973079252e5e41b13d785bb69b8fcd\\transformed\\material-1.5.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,222,308,409,535,618,683,783,853,912,1010,1074,1133,1205,1268,1322,1439,1496,1558,1612,1684,1819,1902,1980,2091,2175,2257,2347,2414,2480,2551,2630,2718,2794,2872,2944,3017,3106,3178,3272,3371,3445,3517,3618,3668,3734,3824,3913,3975,4039,4102,4218,4326,4435,4544", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,85,100,125,82,64,99,69,58,97,63,58,71,62,53,116,56,61,53,71,134,82,77,110,83,81,89,66,65,70,78,87,75,77,71,72,88,71,93,98,73,71,100,49,65,89,88,61,63,62,115,107,108,108,82", "endOffsets": "217,303,404,530,613,678,778,848,907,1005,1069,1128,1200,1263,1317,1434,1491,1553,1607,1679,1814,1897,1975,2086,2170,2252,2342,2409,2475,2546,2625,2713,2789,2867,2939,3012,3101,3173,3267,3366,3440,3512,3613,3663,3729,3819,3908,3970,4034,4097,4213,4321,4430,4539,4622"}, "to": {"startLines": "2,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2968,3791,3892,4018,4170,4327,4427,4497,4556,4654,4718,4777,4849,4912,4966,5083,5140,5202,5256,5328,5463,5546,5624,5735,5819,5901,5991,6058,6124,6195,6274,6362,6438,6516,6588,6661,6750,6822,6916,7015,7089,7161,7262,7312,7378,7468,7557,7619,7683,7746,7862,7970,8079,8497", "endLines": "5,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "endColumns": "12,85,100,125,82,64,99,69,58,97,63,58,71,62,53,116,56,61,53,71,134,82,77,110,83,81,89,66,65,70,78,87,75,77,71,72,88,71,93,98,73,71,100,49,65,89,88,61,63,62,115,107,108,108,82", "endOffsets": "267,3049,3887,4013,4096,4230,4422,4492,4551,4649,4713,4772,4844,4907,4961,5078,5135,5197,5251,5323,5458,5541,5619,5730,5814,5896,5986,6053,6119,6190,6269,6357,6433,6511,6583,6656,6745,6817,6911,7010,7084,7156,7257,7307,7373,7463,7552,7614,7678,7741,7857,7965,8074,8183,8575"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bca5d58794b22aca6036379432e392dd\\transformed\\navigation-ui-2.7.7\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,118", "endOffsets": "156,275"}, "to": {"startLines": "95,96", "startColumns": "4,4", "startOffsets": "8188,8294", "endColumns": "105,118", "endOffsets": "8289,8408"}}]}]}