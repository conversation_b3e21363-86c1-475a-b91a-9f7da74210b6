// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.flexbox.FlexboxLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.theme.widget.ThemeColorFloatingActionButton;
import org.autojs.autojs.theme.widget.ThemeColorTextInputLayout;
import org.autojs.autojs.theme.widget.ThemeColorToolbar;
import org.autojs.autojs.ui.widget.PackageNameEditTextView;
import org.autojs.autojs6.R;

public final class ActivityBuildBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout appConfig;

  @NonNull
  public final ImageView appIcon;

  @NonNull
  public final AppCompatEditText appName;

  @NonNull
  public final ThemeColorFloatingActionButton fab;

  @NonNull
  public final FlexboxLayout flexboxAbis;

  @NonNull
  public final FlexboxLayout flexboxLibraries;

  @NonNull
  public final FlexboxLayout flexboxPermissions;

  @NonNull
  public final TextView manageKeyStore;

  @NonNull
  public final AppCompatEditText outputPath;

  @NonNull
  public final PackageNameEditTextView packageName;

  @NonNull
  public final ThemeColorTextInputLayout packageNameParent;

  @NonNull
  public final ScrollView scrollView;

  @NonNull
  public final ImageView selectOutput;

  @NonNull
  public final ImageView selectSource;

  @NonNull
  public final AppCompatEditText sourcePath;

  @NonNull
  public final LinearLayout sourcePathContainer;

  @NonNull
  public final Spinner spinnerSignatureSchemes;

  @NonNull
  public final Spinner spinnerVerifiedKeyStores;

  @NonNull
  public final TextView textAbis;

  @NonNull
  public final TextView textApkSignConfigs;

  @NonNull
  public final TextView textLibs;

  @NonNull
  public final TextView textPermissions;

  @NonNull
  public final ThemeColorToolbar toolbar;

  @NonNull
  public final AppCompatEditText versionCode;

  @NonNull
  public final ThemeColorTextInputLayout versionCodeParent;

  @NonNull
  public final AppCompatEditText versionName;

  @NonNull
  public final ThemeColorTextInputLayout versionNameParent;

  private ActivityBuildBinding(@NonNull LinearLayout rootView, @NonNull LinearLayout appConfig,
      @NonNull ImageView appIcon, @NonNull AppCompatEditText appName,
      @NonNull ThemeColorFloatingActionButton fab, @NonNull FlexboxLayout flexboxAbis,
      @NonNull FlexboxLayout flexboxLibraries, @NonNull FlexboxLayout flexboxPermissions,
      @NonNull TextView manageKeyStore, @NonNull AppCompatEditText outputPath,
      @NonNull PackageNameEditTextView packageName,
      @NonNull ThemeColorTextInputLayout packageNameParent, @NonNull ScrollView scrollView,
      @NonNull ImageView selectOutput, @NonNull ImageView selectSource,
      @NonNull AppCompatEditText sourcePath, @NonNull LinearLayout sourcePathContainer,
      @NonNull Spinner spinnerSignatureSchemes, @NonNull Spinner spinnerVerifiedKeyStores,
      @NonNull TextView textAbis, @NonNull TextView textApkSignConfigs, @NonNull TextView textLibs,
      @NonNull TextView textPermissions, @NonNull ThemeColorToolbar toolbar,
      @NonNull AppCompatEditText versionCode, @NonNull ThemeColorTextInputLayout versionCodeParent,
      @NonNull AppCompatEditText versionName,
      @NonNull ThemeColorTextInputLayout versionNameParent) {
    this.rootView = rootView;
    this.appConfig = appConfig;
    this.appIcon = appIcon;
    this.appName = appName;
    this.fab = fab;
    this.flexboxAbis = flexboxAbis;
    this.flexboxLibraries = flexboxLibraries;
    this.flexboxPermissions = flexboxPermissions;
    this.manageKeyStore = manageKeyStore;
    this.outputPath = outputPath;
    this.packageName = packageName;
    this.packageNameParent = packageNameParent;
    this.scrollView = scrollView;
    this.selectOutput = selectOutput;
    this.selectSource = selectSource;
    this.sourcePath = sourcePath;
    this.sourcePathContainer = sourcePathContainer;
    this.spinnerSignatureSchemes = spinnerSignatureSchemes;
    this.spinnerVerifiedKeyStores = spinnerVerifiedKeyStores;
    this.textAbis = textAbis;
    this.textApkSignConfigs = textApkSignConfigs;
    this.textLibs = textLibs;
    this.textPermissions = textPermissions;
    this.toolbar = toolbar;
    this.versionCode = versionCode;
    this.versionCodeParent = versionCodeParent;
    this.versionName = versionName;
    this.versionNameParent = versionNameParent;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityBuildBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityBuildBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_build, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityBuildBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.app_config;
      LinearLayout appConfig = ViewBindings.findChildViewById(rootView, id);
      if (appConfig == null) {
        break missingId;
      }

      id = R.id.app_icon;
      ImageView appIcon = ViewBindings.findChildViewById(rootView, id);
      if (appIcon == null) {
        break missingId;
      }

      id = R.id.app_name;
      AppCompatEditText appName = ViewBindings.findChildViewById(rootView, id);
      if (appName == null) {
        break missingId;
      }

      id = R.id.fab;
      ThemeColorFloatingActionButton fab = ViewBindings.findChildViewById(rootView, id);
      if (fab == null) {
        break missingId;
      }

      id = R.id.flexbox_abis;
      FlexboxLayout flexboxAbis = ViewBindings.findChildViewById(rootView, id);
      if (flexboxAbis == null) {
        break missingId;
      }

      id = R.id.flexbox_libraries;
      FlexboxLayout flexboxLibraries = ViewBindings.findChildViewById(rootView, id);
      if (flexboxLibraries == null) {
        break missingId;
      }

      id = R.id.flexbox_permissions;
      FlexboxLayout flexboxPermissions = ViewBindings.findChildViewById(rootView, id);
      if (flexboxPermissions == null) {
        break missingId;
      }

      id = R.id.manage_key_store;
      TextView manageKeyStore = ViewBindings.findChildViewById(rootView, id);
      if (manageKeyStore == null) {
        break missingId;
      }

      id = R.id.output_path;
      AppCompatEditText outputPath = ViewBindings.findChildViewById(rootView, id);
      if (outputPath == null) {
        break missingId;
      }

      id = R.id.package_name;
      PackageNameEditTextView packageName = ViewBindings.findChildViewById(rootView, id);
      if (packageName == null) {
        break missingId;
      }

      id = R.id.package_name_parent;
      ThemeColorTextInputLayout packageNameParent = ViewBindings.findChildViewById(rootView, id);
      if (packageNameParent == null) {
        break missingId;
      }

      id = R.id.scrollView;
      ScrollView scrollView = ViewBindings.findChildViewById(rootView, id);
      if (scrollView == null) {
        break missingId;
      }

      id = R.id.select_output;
      ImageView selectOutput = ViewBindings.findChildViewById(rootView, id);
      if (selectOutput == null) {
        break missingId;
      }

      id = R.id.select_source;
      ImageView selectSource = ViewBindings.findChildViewById(rootView, id);
      if (selectSource == null) {
        break missingId;
      }

      id = R.id.source_path;
      AppCompatEditText sourcePath = ViewBindings.findChildViewById(rootView, id);
      if (sourcePath == null) {
        break missingId;
      }

      id = R.id.source_path_container;
      LinearLayout sourcePathContainer = ViewBindings.findChildViewById(rootView, id);
      if (sourcePathContainer == null) {
        break missingId;
      }

      id = R.id.spinner_signature_schemes;
      Spinner spinnerSignatureSchemes = ViewBindings.findChildViewById(rootView, id);
      if (spinnerSignatureSchemes == null) {
        break missingId;
      }

      id = R.id.spinner_verified_key_stores;
      Spinner spinnerVerifiedKeyStores = ViewBindings.findChildViewById(rootView, id);
      if (spinnerVerifiedKeyStores == null) {
        break missingId;
      }

      id = R.id.text_abis;
      TextView textAbis = ViewBindings.findChildViewById(rootView, id);
      if (textAbis == null) {
        break missingId;
      }

      id = R.id.text_apk_sign_configs;
      TextView textApkSignConfigs = ViewBindings.findChildViewById(rootView, id);
      if (textApkSignConfigs == null) {
        break missingId;
      }

      id = R.id.text_libs;
      TextView textLibs = ViewBindings.findChildViewById(rootView, id);
      if (textLibs == null) {
        break missingId;
      }

      id = R.id.text_permissions;
      TextView textPermissions = ViewBindings.findChildViewById(rootView, id);
      if (textPermissions == null) {
        break missingId;
      }

      id = R.id.toolbar;
      ThemeColorToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.version_code;
      AppCompatEditText versionCode = ViewBindings.findChildViewById(rootView, id);
      if (versionCode == null) {
        break missingId;
      }

      id = R.id.version_code_parent;
      ThemeColorTextInputLayout versionCodeParent = ViewBindings.findChildViewById(rootView, id);
      if (versionCodeParent == null) {
        break missingId;
      }

      id = R.id.version_name;
      AppCompatEditText versionName = ViewBindings.findChildViewById(rootView, id);
      if (versionName == null) {
        break missingId;
      }

      id = R.id.version_name_parent;
      ThemeColorTextInputLayout versionNameParent = ViewBindings.findChildViewById(rootView, id);
      if (versionNameParent == null) {
        break missingId;
      }

      return new ActivityBuildBinding((LinearLayout) rootView, appConfig, appIcon, appName, fab,
          flexboxAbis, flexboxLibraries, flexboxPermissions, manageKeyStore, outputPath,
          packageName, packageNameParent, scrollView, selectOutput, selectSource, sourcePath,
          sourcePathContainer, spinnerSignatureSchemes, spinnerVerifiedKeyStores, textAbis,
          textApkSignConfigs, textLibs, textPermissions, toolbar, versionCode, versionCodeParent,
          versionName, versionNameParent);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
