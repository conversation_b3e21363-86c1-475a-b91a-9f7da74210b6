// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.ui.widget.CommonMarkdownView;
import org.autojs.autojs6.R;

public final class ActivityColorSearchHelpBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final CommonMarkdownView webView;

  @NonNull
  public final FrameLayout webViewParent;

  private ActivityColorSearchHelpBinding(@NonNull CoordinatorLayout rootView,
      @NonNull CommonMarkdownView webView, @NonNull FrameLayout webViewParent) {
    this.rootView = rootView;
    this.webView = webView;
    this.webViewParent = webViewParent;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityColorSearchHelpBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityColorSearchHelpBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_color_search_help, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityColorSearchHelpBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.web_view;
      CommonMarkdownView webView = ViewBindings.findChildViewById(rootView, id);
      if (webView == null) {
        break missingId;
      }

      id = R.id.web_view_parent;
      FrameLayout webViewParent = ViewBindings.findChildViewById(rootView, id);
      if (webViewParent == null) {
        break missingId;
      }

      return new ActivityColorSearchHelpBinding((CoordinatorLayout) rootView, webView,
          webViewParent);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
