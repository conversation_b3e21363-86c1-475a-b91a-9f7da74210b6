// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Guideline;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.jaredrummler.android.widget.AnimatedSvgView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.theme.widget.ThemeColorToolbar;
import org.autojs.autojs6.R;

public final class ActivityAboutBinding implements ViewBinding {
  @NonNull
  private final View rootView;

  @NonNull
  public final ActivityAboutFunctionButtonsBinding activityAboutFunctionButtons;

  @NonNull
  public final LinearLayout avatarDeveloper;

  @NonNull
  public final LinearLayout avatarDeveloperUserContents;

  @NonNull
  public final LinearLayout avatarOriginalDeveloper;

  @NonNull
  public final LinearLayout avatarOriginalDeveloperUserContents;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout-land/</li>
   *   <li>layout-sw400dp-land/</li>
   * </ul>
   */
  @Nullable
  public final LinearLayout developersContainer;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-land/</li>
   *   <li>layout-sw400dp-land/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final LinearLayout icon1stDeveloperContainer;

  @NonNull
  public final LinearLayout icon1stDeveloperIdentifier;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-land/</li>
   *   <li>layout-sw400dp-land/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final LinearLayout icon2ndDeveloperContainer;

  @NonNull
  public final LinearLayout icon2ndDeveloperIdentifier;

  @NonNull
  public final ImageView iconAboutApp;

  @NonNull
  public final AnimatedSvgView iconAboutAppSvgView;

  @NonNull
  public final LinearLayout iconContainer;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout-land/</li>
   *   <li>layout-sw400dp-land/</li>
   * </ul>
   */
  @Nullable
  public final ConstraintLayout mainContentContainer;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout-land/</li>
   *   <li>layout-sw400dp-land/</li>
   * </ul>
   */
  @Nullable
  public final Guideline mainContentGuideline45;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout-land/</li>
   *   <li>layout-sw400dp-land/</li>
   * </ul>
   */
  @Nullable
  public final Guideline mainContentGuideline55;

  @NonNull
  public final TextView since;

  @NonNull
  public final ThemeColorToolbar toolbar;

  @NonNull
  public final TextView version;

  private ActivityAboutBinding(@NonNull View rootView,
      @NonNull ActivityAboutFunctionButtonsBinding activityAboutFunctionButtons,
      @NonNull LinearLayout avatarDeveloper, @NonNull LinearLayout avatarDeveloperUserContents,
      @NonNull LinearLayout avatarOriginalDeveloper,
      @NonNull LinearLayout avatarOriginalDeveloperUserContents,
      @Nullable LinearLayout developersContainer, @Nullable LinearLayout icon1stDeveloperContainer,
      @NonNull LinearLayout icon1stDeveloperIdentifier,
      @Nullable LinearLayout icon2ndDeveloperContainer,
      @NonNull LinearLayout icon2ndDeveloperIdentifier, @NonNull ImageView iconAboutApp,
      @NonNull AnimatedSvgView iconAboutAppSvgView, @NonNull LinearLayout iconContainer,
      @Nullable ConstraintLayout mainContentContainer, @Nullable Guideline mainContentGuideline45,
      @Nullable Guideline mainContentGuideline55, @NonNull TextView since,
      @NonNull ThemeColorToolbar toolbar, @NonNull TextView version) {
    this.rootView = rootView;
    this.activityAboutFunctionButtons = activityAboutFunctionButtons;
    this.avatarDeveloper = avatarDeveloper;
    this.avatarDeveloperUserContents = avatarDeveloperUserContents;
    this.avatarOriginalDeveloper = avatarOriginalDeveloper;
    this.avatarOriginalDeveloperUserContents = avatarOriginalDeveloperUserContents;
    this.developersContainer = developersContainer;
    this.icon1stDeveloperContainer = icon1stDeveloperContainer;
    this.icon1stDeveloperIdentifier = icon1stDeveloperIdentifier;
    this.icon2ndDeveloperContainer = icon2ndDeveloperContainer;
    this.icon2ndDeveloperIdentifier = icon2ndDeveloperIdentifier;
    this.iconAboutApp = iconAboutApp;
    this.iconAboutAppSvgView = iconAboutAppSvgView;
    this.iconContainer = iconContainer;
    this.mainContentContainer = mainContentContainer;
    this.mainContentGuideline45 = mainContentGuideline45;
    this.mainContentGuideline55 = mainContentGuideline55;
    this.since = since;
    this.toolbar = toolbar;
    this.version = version;
  }

  @Override
  @NonNull
  public View getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAboutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAboutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_about, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAboutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.activity_about_function_buttons;
      View activityAboutFunctionButtons = ViewBindings.findChildViewById(rootView, id);
      if (activityAboutFunctionButtons == null) {
        break missingId;
      }
      ActivityAboutFunctionButtonsBinding binding_activityAboutFunctionButtons = ActivityAboutFunctionButtonsBinding.bind(activityAboutFunctionButtons);

      id = R.id.avatar_developer;
      LinearLayout avatarDeveloper = ViewBindings.findChildViewById(rootView, id);
      if (avatarDeveloper == null) {
        break missingId;
      }

      id = R.id.avatar_developer_user_contents;
      LinearLayout avatarDeveloperUserContents = ViewBindings.findChildViewById(rootView, id);
      if (avatarDeveloperUserContents == null) {
        break missingId;
      }

      id = R.id.avatar_original_developer;
      LinearLayout avatarOriginalDeveloper = ViewBindings.findChildViewById(rootView, id);
      if (avatarOriginalDeveloper == null) {
        break missingId;
      }

      id = R.id.avatar_original_developer_user_contents;
      LinearLayout avatarOriginalDeveloperUserContents = ViewBindings.findChildViewById(rootView, id);
      if (avatarOriginalDeveloperUserContents == null) {
        break missingId;
      }

      id = R.id.developers_container;
      LinearLayout developersContainer = ViewBindings.findChildViewById(rootView, id);

      id = R.id.icon_1st_developer_container;
      LinearLayout icon1stDeveloperContainer = ViewBindings.findChildViewById(rootView, id);

      id = R.id.icon_1st_developer_identifier;
      LinearLayout icon1stDeveloperIdentifier = ViewBindings.findChildViewById(rootView, id);
      if (icon1stDeveloperIdentifier == null) {
        break missingId;
      }

      id = R.id.icon_2nd_developer_container;
      LinearLayout icon2ndDeveloperContainer = ViewBindings.findChildViewById(rootView, id);

      id = R.id.icon_2nd_developer_identifier;
      LinearLayout icon2ndDeveloperIdentifier = ViewBindings.findChildViewById(rootView, id);
      if (icon2ndDeveloperIdentifier == null) {
        break missingId;
      }

      id = R.id.icon_about_app;
      ImageView iconAboutApp = ViewBindings.findChildViewById(rootView, id);
      if (iconAboutApp == null) {
        break missingId;
      }

      id = R.id.icon_about_app_svg_view;
      AnimatedSvgView iconAboutAppSvgView = ViewBindings.findChildViewById(rootView, id);
      if (iconAboutAppSvgView == null) {
        break missingId;
      }

      id = R.id.icon_container;
      LinearLayout iconContainer = ViewBindings.findChildViewById(rootView, id);
      if (iconContainer == null) {
        break missingId;
      }

      id = R.id.main_content_container;
      ConstraintLayout mainContentContainer = ViewBindings.findChildViewById(rootView, id);

      id = R.id.main_content_guideline_45;
      Guideline mainContentGuideline45 = ViewBindings.findChildViewById(rootView, id);

      id = R.id.main_content_guideline_55;
      Guideline mainContentGuideline55 = ViewBindings.findChildViewById(rootView, id);

      id = R.id.since;
      TextView since = ViewBindings.findChildViewById(rootView, id);
      if (since == null) {
        break missingId;
      }

      id = R.id.toolbar;
      ThemeColorToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.version;
      TextView version = ViewBindings.findChildViewById(rootView, id);
      if (version == null) {
        break missingId;
      }

      return new ActivityAboutBinding(rootView, binding_activityAboutFunctionButtons,
          avatarDeveloper, avatarDeveloperUserContents, avatarOriginalDeveloper,
          avatarOriginalDeveloperUserContents, developersContainer, icon1stDeveloperContainer,
          icon1stDeveloperIdentifier, icon2ndDeveloperContainer, icon2ndDeveloperIdentifier,
          iconAboutApp, iconAboutAppSvgView, iconContainer, mainContentContainer,
          mainContentGuideline45, mainContentGuideline55, since, toolbar, version);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
