// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Guideline;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs6.R;

public final class ActivityAboutFunctionButtonsBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ConstraintLayout aboutFunctionsButtonFeedback;

  @NonNull
  public final ImageView aboutFunctionsButtonFeedbackIcon;

  @NonNull
  public final ConstraintLayout aboutFunctionsButtonLicenses;

  @NonNull
  public final ImageView aboutFunctionsButtonLicensesIcon;

  @NonNull
  public final ConstraintLayout aboutFunctionsButtonUpdate;

  @NonNull
  public final ImageView aboutFunctionsButtonUpdateIcon;

  @NonNull
  public final ConstraintLayout aboutFunctionsButtonVersionHistories;

  @NonNull
  public final ImageView aboutFunctionsButtonVersionHistoriesIcon;

  @NonNull
  public final Guideline guideline50Feedback;

  @NonNull
  public final Guideline guideline50Licenses;

  @NonNull
  public final Guideline guideline50Update;

  @NonNull
  public final Guideline guideline50VersionHistories;

  @NonNull
  public final View space01;

  @NonNull
  public final View space12;

  @NonNull
  public final View space23;

  @NonNull
  public final View space34;

  @NonNull
  public final View space4End;

  private ActivityAboutFunctionButtonsBinding(@NonNull ConstraintLayout rootView,
      @NonNull ConstraintLayout aboutFunctionsButtonFeedback,
      @NonNull ImageView aboutFunctionsButtonFeedbackIcon,
      @NonNull ConstraintLayout aboutFunctionsButtonLicenses,
      @NonNull ImageView aboutFunctionsButtonLicensesIcon,
      @NonNull ConstraintLayout aboutFunctionsButtonUpdate,
      @NonNull ImageView aboutFunctionsButtonUpdateIcon,
      @NonNull ConstraintLayout aboutFunctionsButtonVersionHistories,
      @NonNull ImageView aboutFunctionsButtonVersionHistoriesIcon,
      @NonNull Guideline guideline50Feedback, @NonNull Guideline guideline50Licenses,
      @NonNull Guideline guideline50Update, @NonNull Guideline guideline50VersionHistories,
      @NonNull View space01, @NonNull View space12, @NonNull View space23, @NonNull View space34,
      @NonNull View space4End) {
    this.rootView = rootView;
    this.aboutFunctionsButtonFeedback = aboutFunctionsButtonFeedback;
    this.aboutFunctionsButtonFeedbackIcon = aboutFunctionsButtonFeedbackIcon;
    this.aboutFunctionsButtonLicenses = aboutFunctionsButtonLicenses;
    this.aboutFunctionsButtonLicensesIcon = aboutFunctionsButtonLicensesIcon;
    this.aboutFunctionsButtonUpdate = aboutFunctionsButtonUpdate;
    this.aboutFunctionsButtonUpdateIcon = aboutFunctionsButtonUpdateIcon;
    this.aboutFunctionsButtonVersionHistories = aboutFunctionsButtonVersionHistories;
    this.aboutFunctionsButtonVersionHistoriesIcon = aboutFunctionsButtonVersionHistoriesIcon;
    this.guideline50Feedback = guideline50Feedback;
    this.guideline50Licenses = guideline50Licenses;
    this.guideline50Update = guideline50Update;
    this.guideline50VersionHistories = guideline50VersionHistories;
    this.space01 = space01;
    this.space12 = space12;
    this.space23 = space23;
    this.space34 = space34;
    this.space4End = space4End;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAboutFunctionButtonsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAboutFunctionButtonsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_about_function_buttons, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAboutFunctionButtonsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.about_functions_button_feedback;
      ConstraintLayout aboutFunctionsButtonFeedback = ViewBindings.findChildViewById(rootView, id);
      if (aboutFunctionsButtonFeedback == null) {
        break missingId;
      }

      id = R.id.about_functions_button_feedback_icon;
      ImageView aboutFunctionsButtonFeedbackIcon = ViewBindings.findChildViewById(rootView, id);
      if (aboutFunctionsButtonFeedbackIcon == null) {
        break missingId;
      }

      id = R.id.about_functions_button_licenses;
      ConstraintLayout aboutFunctionsButtonLicenses = ViewBindings.findChildViewById(rootView, id);
      if (aboutFunctionsButtonLicenses == null) {
        break missingId;
      }

      id = R.id.about_functions_button_licenses_icon;
      ImageView aboutFunctionsButtonLicensesIcon = ViewBindings.findChildViewById(rootView, id);
      if (aboutFunctionsButtonLicensesIcon == null) {
        break missingId;
      }

      id = R.id.about_functions_button_update;
      ConstraintLayout aboutFunctionsButtonUpdate = ViewBindings.findChildViewById(rootView, id);
      if (aboutFunctionsButtonUpdate == null) {
        break missingId;
      }

      id = R.id.about_functions_button_update_icon;
      ImageView aboutFunctionsButtonUpdateIcon = ViewBindings.findChildViewById(rootView, id);
      if (aboutFunctionsButtonUpdateIcon == null) {
        break missingId;
      }

      id = R.id.about_functions_button_version_histories;
      ConstraintLayout aboutFunctionsButtonVersionHistories = ViewBindings.findChildViewById(rootView, id);
      if (aboutFunctionsButtonVersionHistories == null) {
        break missingId;
      }

      id = R.id.about_functions_button_version_histories_icon;
      ImageView aboutFunctionsButtonVersionHistoriesIcon = ViewBindings.findChildViewById(rootView, id);
      if (aboutFunctionsButtonVersionHistoriesIcon == null) {
        break missingId;
      }

      id = R.id.guideline_50_feedback;
      Guideline guideline50Feedback = ViewBindings.findChildViewById(rootView, id);
      if (guideline50Feedback == null) {
        break missingId;
      }

      id = R.id.guideline_50_licenses;
      Guideline guideline50Licenses = ViewBindings.findChildViewById(rootView, id);
      if (guideline50Licenses == null) {
        break missingId;
      }

      id = R.id.guideline_50_update;
      Guideline guideline50Update = ViewBindings.findChildViewById(rootView, id);
      if (guideline50Update == null) {
        break missingId;
      }

      id = R.id.guideline_50_version_histories;
      Guideline guideline50VersionHistories = ViewBindings.findChildViewById(rootView, id);
      if (guideline50VersionHistories == null) {
        break missingId;
      }

      id = R.id.space_0_1;
      View space01 = ViewBindings.findChildViewById(rootView, id);
      if (space01 == null) {
        break missingId;
      }

      id = R.id.space_1_2;
      View space12 = ViewBindings.findChildViewById(rootView, id);
      if (space12 == null) {
        break missingId;
      }

      id = R.id.space_2_3;
      View space23 = ViewBindings.findChildViewById(rootView, id);
      if (space23 == null) {
        break missingId;
      }

      id = R.id.space_3_4;
      View space34 = ViewBindings.findChildViewById(rootView, id);
      if (space34 == null) {
        break missingId;
      }

      id = R.id.space_4_end;
      View space4End = ViewBindings.findChildViewById(rootView, id);
      if (space4End == null) {
        break missingId;
      }

      return new ActivityAboutFunctionButtonsBinding((ConstraintLayout) rootView,
          aboutFunctionsButtonFeedback, aboutFunctionsButtonFeedbackIcon,
          aboutFunctionsButtonLicenses, aboutFunctionsButtonLicensesIcon,
          aboutFunctionsButtonUpdate, aboutFunctionsButtonUpdateIcon,
          aboutFunctionsButtonVersionHistories, aboutFunctionsButtonVersionHistoriesIcon,
          guideline50Feedback, guideline50Licenses, guideline50Update, guideline50VersionHistories,
          space01, space12, space23, space34, space4End);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
