{"logs": [{"outputFile": "com.bm.atool.app-mergeDebugResources-43:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\29973079252e5e41b13d785bb69b8fcd\\transformed\\material-1.5.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,329,417,513,629,712,779,870,936,999,1087,1154,1212,1283,1342,1396,1510,1570,1633,1687,1760,1879,1965,2048,2157,2242,2329,2417,2484,2550,2622,2698,2788,2861,2938,3019,3093,3183,3262,3353,3449,3523,3604,3699,3753,3819,3906,3992,4054,4118,4181,4288,4380,4478,4570", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,87,95,115,82,66,90,65,62,87,66,57,70,58,53,113,59,62,53,72,118,85,82,108,84,86,87,66,65,71,75,89,72,76,80,73,89,78,90,95,73,80,94,53,65,86,85,61,63,62,106,91,97,91,81", "endOffsets": "324,412,508,624,707,774,865,931,994,1082,1149,1207,1278,1337,1391,1505,1565,1628,1682,1755,1874,1960,2043,2152,2237,2324,2412,2479,2545,2617,2693,2783,2856,2933,3014,3088,3178,3257,3348,3444,3518,3599,3694,3748,3814,3901,3987,4049,4113,4176,4283,4375,4473,4565,4647"}, "to": {"startLines": "2,35,43,44,45,47,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3108,3923,4019,4135,4292,4447,4538,4604,4667,4755,4822,4880,4951,5010,5064,5178,5238,5301,5355,5428,5547,5633,5716,5825,5910,5997,6085,6152,6218,6290,6366,6456,6529,6606,6687,6761,6851,6930,7021,7117,7191,7272,7367,7421,7487,7574,7660,7722,7786,7849,7956,8048,8146,8547", "endLines": "7,35,43,44,45,47,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100", "endColumns": "12,87,95,115,82,66,90,65,62,87,66,57,70,58,53,113,59,62,53,72,118,85,82,108,84,86,87,66,65,71,75,89,72,76,80,73,89,78,90,95,73,80,94,53,65,86,85,61,63,62,106,91,97,91,81", "endOffsets": "374,3191,4014,4130,4213,4354,4533,4599,4662,4750,4817,4875,4946,5005,5059,5173,5233,5296,5350,5423,5542,5628,5711,5820,5905,5992,6080,6147,6213,6285,6361,6451,6524,6601,6682,6756,6846,6925,7016,7112,7186,7267,7362,7416,7482,7569,7655,7717,7781,7844,7951,8043,8141,8233,8624"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bca5d58794b22aca6036379432e392dd\\transformed\\navigation-ui-2.7.7\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,119", "endOffsets": "158,278"}, "to": {"startLines": "97,98", "startColumns": "4,4", "startOffsets": "8238,8346", "endColumns": "107,119", "endOffsets": "8341,8461"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\cee19163c10f1fafd81d90a045427d43\\transformed\\core-1.13.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "36,37,38,39,40,41,42,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3196,3296,3398,3499,3600,3705,3810,8853", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "3291,3393,3494,3595,3700,3805,3918,8949"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4b1b6d71ac725a2fc4ab91a425e45a1c\\transformed\\appcompat-1.7.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,816,898,989,1082,1177,1271,1371,1464,1559,1654,1745,1836,1935,2041,2147,2245,2352,2459,2564,2734,2834", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,811,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829,2911"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "379,488,590,698,784,889,1007,1090,1172,1263,1356,1451,1545,1645,1738,1833,1928,2019,2110,2209,2315,2421,2519,2626,2733,2838,3008,8771", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "483,585,693,779,884,1002,1085,1167,1258,1351,1446,1540,1640,1733,1828,1923,2014,2105,2204,2310,2416,2514,2621,2728,2833,3003,3103,8848"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\466bd753c0bc663f243f57f85dfa0cbd\\transformed\\preference-1.2.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,267,348,490,659,744", "endColumns": "73,87,80,141,168,84,82", "endOffsets": "174,262,343,485,654,739,822"}, "to": {"startLines": "46,48,99,101,104,105,106", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4218,4359,8466,8629,8954,9123,9208", "endColumns": "73,87,80,141,168,84,82", "endOffsets": "4287,4442,8542,8766,9118,9203,9286"}}]}]}