1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    xmlns:tools="http://schemas.android.com/tools"
4    package="org.autojs.autojs6" >
5
6    <uses-sdk android:minSdkVersion="24" />
7
8    <uses-feature
8-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:5:5-7:36
9        android:name="android.hardware.telephony"
9-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:6:9-50
10        android:required="false" />
10-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:7:9-33
11    <uses-feature
11-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:9:5-11:36
12        android:name="android.hardware.camera"
12-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:10:9-47
13        android:required="false" />
13-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:11:9-33
14
15    <!-- @Hint by SuperMonster003 on May 27, 2022. -->
16    <!-- ! -->
17    <!-- ! WRT: android.permission.WRITE_EXTERNAL_STORAGE -->
18    <!-- ! MSV<API>: android:maxSdkVersion="API" -->
19    <!-- ! LGC: android:requestLegacyExternalStorage="true" (since API 29) -->
20    <!-- ! MAN: android.permission.MANAGE_EXTERNAL_STORAGE (since API 30) -->
21    <!-- ! -->
22    <!-- ! Storage permission requirement on different API targets. -->
23    <!-- ! [ XXX ] means optional but recommended. -->
24    <!-- ! zh-CN: -->
25    <!-- ! 存储权限对应不同的 API 级别. -->
26    <!-- ! [ XXX ] 代表可选但推荐满足其要求. -->
27    <!-- ! -->
28    <!-- ! Target API == 28: WRT -->
29    <!-- ! Target API == 29: WRT + [ MSV<29> ] / WRT + [ MSV<28> ] + LGC -->
30    <!-- ! Target API >= 30: WRT + [ MSV<29> ] + LGC  + MAN -->
31
32    <uses-permission
32-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:30:5-32:38
33        android:name="android.permission.READ_EXTERNAL_STORAGE"
33-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:31:9-64
34        android:maxSdkVersion="32" />
34-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:32:9-35
35    <uses-permission
35-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:34:5-36:38
36        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
36-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:35:9-65
37        android:maxSdkVersion="29" />
37-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:36:9-35
38    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
38-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:38:5-76
38-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:38:22-73
39    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
39-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:39:5-75
39-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:39:22-72
40    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
40-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:40:5-75
40-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:40:22-72
41    <uses-permission
41-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:42:5-44:40
42        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
42-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:43:9-66
43        tools:ignore="ScopedStorage" />
43-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:44:9-37
44    <uses-permission
44-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:46:5-49:47
45        android:name="android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS"
45-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:47:9-73
46        android:maxSdkVersion="31"
46-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:48:9-35
47        tools:ignore="ProtectedPermissions" />
47-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:49:9-44
48    <uses-permission
48-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:51:5-53:47
49        android:name="android.permission.READ_PRIVILEGED_PHONE_STATE"
49-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:52:9-70
50        tools:ignore="ProtectedPermissions" />
50-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:53:9-44
51    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />
51-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:55:5-88
51-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:55:22-85
52    <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" />
52-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:56:5-90
52-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:56:22-87
53    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
53-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:58:5-83
53-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:58:22-80
54    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES" />
54-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:59:5-82
54-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:59:22-79
55    <uses-permission android:name="android.permission.WAKE_LOCK" />
55-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:61:5-68
55-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:61:22-65
56    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
56-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:63:5-74
56-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:63:22-71
57    <uses-permission
57-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:64:5-66:47
58        android:name="android.permission.SCHEDULE_EXACT_ALARM"
58-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:65:9-63
59        tools:ignore="ProtectedPermissions" />
59-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:66:9-44
60    <uses-permission android:name="android.permission.VIBRATE" />
60-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:68:5-66
60-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:68:22-63
61    <uses-permission android:name="android.permission.INTERNET" />
61-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:70:5-67
61-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:70:22-64
62    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
62-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:71:5-76
62-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:71:22-73
63    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
63-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:72:5-79
63-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:72:22-76
64    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
64-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:73:5-76
64-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:73:22-73
65    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
65-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:74:5-79
65-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:74:22-76
66    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
66-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:75:5-86
66-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:75:22-83
67    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
67-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:77:5-78
67-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:77:22-75
68    <uses-permission android:name="android.permission.REORDER_TASKS" />
68-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:79:5-72
68-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:79:22-69
69    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
69-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:81:5-95
69-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:81:22-92
70    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
70-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:83:5-81
70-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:83:22-78
71
72    <!-- To allow posting notifications on Android 13. -->
73    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
73-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:86:5-77
73-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:86:22-74
74    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
74-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:88:5-77
74-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:88:22-74
75    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
75-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:89:5-94
75-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:89:22-91
76    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
76-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:90:5-89
76-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:90:22-86
77    <uses-permission
77-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:92:5-94:47
78        android:name="android.permission.UNLIMITED_TOASTS"
78-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:93:9-59
79        tools:ignore="ProtectedPermissions" />
79-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:94:9-44
80    <uses-permission
80-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:96:5-98:47
81        android:name="android.permission.CAPTURE_VIDEO_OUTPUT"
81-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:97:9-63
82        tools:ignore="ProtectedPermissions" />
82-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:98:9-44
83    <uses-permission
83-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:100:5-102:47
84        android:name="android.permission.DUMP"
84-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:101:9-47
85        tools:ignore="ProtectedPermissions" />
85-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:102:9-44
86    <uses-permission
86-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:104:5-106:53
87        android:name="android.permission.QUERY_ALL_PACKAGES"
87-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:105:9-61
88        tools:ignore="QueryAllPackagesPermission" />
88-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:106:9-50
89    <uses-permission
89-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:108:5-110:47
90        android:name="android.permission.PACKAGE_USAGE_STATS"
90-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:109:9-62
91        tools:ignore="ProtectedPermissions" />
91-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:110:9-44
92    <uses-permission
92-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:112:5-114:47
93        android:name="android.permission.WRITE_SETTINGS"
93-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:113:9-57
94        tools:ignore="ProtectedPermissions" />
94-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:114:9-44
95    <uses-permission
95-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:116:5-118:47
96        android:name="android.permission.WRITE_SECURE_SETTINGS"
96-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:117:9-64
97        tools:ignore="ProtectedPermissions" />
97-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:118:9-44
98    <uses-permission
98-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:120:5-122:47
99        android:name="android.permission.MANAGE_USERS"
99-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:121:9-55
100        tools:ignore="ProtectedPermissions" />
100-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:122:9-44
101    <uses-permission
101-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:124:5-127:47
102        android:name="android.permission.INTERACT_ACROSS_USERS_FULL"
102-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:125:9-69
103        android:protectionLevel="signature"
103-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:126:9-44
104        tools:ignore="ProtectedPermissions" />
104-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:127:9-44
105
106    <!-- 非 AutoJs6 运行必需, 不会主动申请, 脚本可自行申请 -->
107
108    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
108-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:131:5-81
108-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:131:22-78
109    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
109-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:133:5-79
109-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:133:22-76
110    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
110-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:134:5-89
110-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:134:22-86
111    <uses-permission android:name="android.permission.RECORD_AUDIO" />
111-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:136:5-71
111-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:136:22-68
112    <uses-permission android:name="com.termux.permission.RUN_COMMAND" />
112-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:138:5-73
112-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:138:22-70
113
114    <!-- Dangerous permission witch should be treated carefully. -->
115
116    <uses-permission android:name="android.permission.READ_CONTACTS" />
116-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:142:5-72
116-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:142:22-69
117    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
117-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:143:5-73
117-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:143:22-70
118    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
118-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:145:5-75
118-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:145:22-72
119    <uses-permission android:name="android.permission.CALL_PHONE" />
119-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:146:5-69
119-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:146:22-66
120    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
120-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:147:5-80
120-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:147:22-77
121    <uses-permission android:name="android.permission.READ_SMS" />
121-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:149:5-67
121-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:149:22-64
122    <uses-permission android:name="android.permission.SEND_SMS" />
122-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:150:5-67
122-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:150:22-64
123    <uses-permission android:name="android.permission.RECEIVE_SMS" />
123-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:151:5-70
123-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:151:22-67
124    <uses-permission android:name="android.permission.CAMERA" />
124-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:153:5-65
124-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:153:22-62
125    <uses-permission android:name="android.permission.FLASHLIGHT" />
125-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:154:5-69
125-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:154:22-66
126    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />
126-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:156:5-76
126-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:156:22-73
127    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
127-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:157:5-71
127-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:157:22-68
128    <uses-permission android:name="android.permission.READ_CALENDAR" />
128-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:158:5-72
128-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:158:22-69
129    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
129-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:159:5-73
129-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:159:22-70
130    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
130-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:161:5-75
130-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:161:22-72
131    <uses-permission android:name="android.permission.BLUETOOTH" />
131-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:162:5-68
131-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:162:22-65
132    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
132-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:163:5-74
132-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:163:22-71
133    <uses-permission android:name="android.permission.SET_WALLPAPER" />
133-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:165:5-72
133-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:165:22-69
134    <uses-permission android:name="android.permission.SET_WALLPAPER_HINTS" />
134-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:166:5-78
134-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:166:22-75
135    <uses-permission
135-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:168:5-170:47
136        android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
136-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:169:9-68
137        tools:ignore="ProtectedPermissions" />
137-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:170:9-44
138    <uses-permission
138-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:171:5-173:47
139        android:name="android.permission.MOUNT_FORMAT_FILESYSTEMS"
139-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:172:9-67
140        tools:ignore="ProtectedPermissions" />
140-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:173:9-44
141    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
141-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:175:5-84
141-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:175:22-81
142    <uses-permission android:name="android.permission.NFC" />
142-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:177:5-62
142-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:177:22-59
143    <uses-permission android:name="moe.shizuku.manager.permission.API_V23" />
143-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:179:5-78
143-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:179:22-75
144    <uses-permission android:name="com.android.vending.BILLING" />
144-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:181:5-67
144-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:181:22-64
145
146    <!-- Property "largeHeap" is not recommended to set to true. -->
147    <!-- https://stackoverflow.com/questions/27396892/what-are-advantages-of-setting-largeheap-to-true -->
148    <!-- However, "largeHeap" may be helpful for prevent LeakCanary from OOM when analyzing detected memory leaks. -->
149    <application
149-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:186:5-723:19
150        android:name="org.autojs.autojs.App"
150-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:187:9-45
151        android:allowBackup="false"
151-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:188:9-36
152        android:hardwareAccelerated="true"
152-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:189:9-43
153        android:icon="${icon}"
153-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:190:9-31
154        android:label="${appName}"
154-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:191:9-35
155        android:largeHeap="true"
155-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:192:9-33
156        android:localeConfig="@xml/locales_config"
156-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:196:9-51
157        android:requestLegacyExternalStorage="true"
157-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:193:9-52
158        android:supportsRtl="true"
158-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:194:9-35
159        android:theme="@style/AppTheme"
159-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:195:9-40
160        android:usesCleartextTraffic="true"
160-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:197:9-44
161        tools:ignore="DataExtractionRules"
161-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:198:9-43
162        tools:targetApi="tiramisu" >
162-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:199:9-35
163        <meta-data
163-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:201:9-203:35
164            android:name="android.max_aspect"
164-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:202:13-46
165            android:value="2.1" />
165-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:203:13-32
166        <meta-data
166-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:205:9-207:42
167            android:name="CHANNEL"
167-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:206:13-35
168            android:value="${CHANNEL}" />
168-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:207:13-39
169
170        <activity
170-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:209:9-227:20
171            android:name="org.autojs.autojs.ui.splash.SplashActivity"
171-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:210:13-70
172            android:exported="true"
172-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:211:13-36
173            android:icon="@drawable/autojs6_material"
173-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:212:13-54
174            android:label="@string/app_name"
174-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:213:13-45
175            android:theme="@style/AppTheme.Splash"
175-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:214:13-51
176            tools:ignore="RedundantLabel" >
176-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:215:13-42
177            <intent-filter>
177-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:216:13-222:29
178                <action android:name="android.intent.action.MAIN" />
178-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:218:17-69
178-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:218:25-66
179
180                <category android:name="${intentCategory}" />
180-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:220:17-62
180-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:220:27-59
181            </intent-filter>
182
183            <meta-data
183-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:224:13-226:57
184                android:name="android.app.shortcuts"
184-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:225:17-53
185                android:resource="@xml/app_shortcuts" />
185-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:226:17-54
186        </activity>
187        <activity
187-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:229:9-233:58
188            android:name="org.autojs.autojs.ui.main.MainActivity"
188-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:230:13-66
189            android:exported="true"
189-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:231:13-36
190            android:launchMode="singleTask"
190-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:232:13-44
191            android:theme="@style/AppTheme.FullScreen" />
191-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:233:13-55
192        <activity
192-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:235:9-237:56
193            android:name="org.autojs.autojs.ui.main.scripts.DisplayManifestActivity"
193-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:236:13-85
194            android:theme="@style/AppTheme.Settings" />
194-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:237:13-53
195        <activity
195-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:239:9-241:56
196            android:name="org.autojs.autojs.ui.main.scripts.DisplayMediaInfoActivity"
196-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:240:13-86
197            android:theme="@style/AppTheme.Settings" />
197-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:241:13-53
198        <activity
198-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:243:9-245:56
199            android:name="org.autojs.autojs.ui.settings.DisplayVersionHistoriesActivity"
199-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:244:13-89
200            android:theme="@style/AppTheme.Settings" />
200-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:245:13-53
201        <activity
201-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:247:9-259:20
202            android:name="org.autojs.autojs.external.shortcut.ShortcutActivity"
202-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:248:13-80
203            android:exported="true"
203-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:249:13-36
204            android:taskAffinity="org.autojs.autojs.external.shortcut.ShortcutActivity"
204-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:250:13-88
205            android:theme="@android:style/Theme.NoDisplay" >
205-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:251:13-59
206            <intent-filter>
206-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:252:13-258:29
207                <action android:name="android.intent.action.MAIN" />
207-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:218:17-69
207-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:218:25-66
208
209                <category android:name="android.intent.category.DEFAULT" />
209-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:256:17-76
209-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:256:27-73
210            </intent-filter>
211        </activity>
212
213        <provider
213-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:261:9-269:20
214            android:name="androidx.core.content.FileProvider"
214-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:262:13-62
215            android:authorities="${authorities}"
215-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:263:13-49
216            android:exported="false"
216-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:264:13-37
217            android:grantUriPermissions="true" >
217-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:265:13-47
218            <meta-data
218-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:266:13-268:58
219                android:name="android.support.FILE_PROVIDER_PATHS"
219-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:267:17-67
220                android:resource="@xml/provider_paths" />
220-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:268:17-55
221        </provider>
222
223        <activity
223-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:271:9-277:50
224            android:name="org.autojs.autojs.ui.edit.EditActivity"
224-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:272:13-66
225            android:configChanges="orientation|keyboardHidden|screenSize|locale"
225-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:273:13-81
226            android:launchMode="standard"
226-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:274:13-42
227            android:multiprocess="true"
227-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:275:13-40
228            android:taskAffinity="org.autojs.autojs.edit"
228-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:276:13-58
229            android:theme="@style/EditorTheme" />
229-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:277:13-47
230        <activity
230-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:279:9-281:56
231            android:name="org.autojs.autojs.ui.settings.AboutActivity"
231-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:280:13-71
232            android:theme="@style/AppTheme.Settings" />
232-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:281:13-53
233        <activity
233-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:283:9-287:56
234            android:name="org.autojs.autojs.ui.settings.PreferencesActivity"
234-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:284:13-77
235            android:exported="true"
235-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:285:13-36
236            android:label="@string/text_app_shortcut_settings_long_label"
236-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:286:13-74
237            android:theme="@style/AppTheme.Settings" />
237-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:287:13-53
238        <activity
238-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:289:9-291:56
239            android:name="org.autojs.autojs.ui.settings.DeveloperOptionsActivity"
239-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:290:13-82
240            android:theme="@style/AppTheme.Settings" />
240-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:291:13-53
241
242        <!-- <activity android:name="org.autojs.autojs.ui.error.ErrorReportActivity" android:process=":crash_report"/> -->
243        <activity android:name="org.autojs.autojs.ui.error.ErrorReportActivity" />
243-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:294:9-83
243-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:294:19-80
244        <activity
244-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:296:9-298:84
245            android:name="org.autojs.autojs.external.tasker.TaskerScriptEditActivity"
245-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:297:13-86
246            android:configChanges="orientation|keyboardHidden|screenSize|locale" />
246-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:298:13-81
247        <activity
247-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:300:9-302:84
248            android:name="org.autojs.autojs.ui.project.BuildActivity"
248-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:301:13-70
249            android:configChanges="orientation|keyboardHidden|screenSize|locale" />
249-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:302:13-81
250        <activity
250-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:304:9-307:40
251            android:name="org.autojs.autojs.ui.error.ErrorDialogActivity"
251-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:305:13-74
252            android:exported="false"
252-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:307:13-37
253            android:theme="@style/AppTheme.Transparent" />
253-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:306:13-56
254        <activity android:name="org.autojs.autojs.ui.project.ProjectConfigActivity" />
254-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:309:9-87
254-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:309:19-84
255        <activity android:name="org.autojs.autojs.ui.keystore.ManageKeyStoreActivity" />
255-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:311:9-89
255-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:311:19-86
256        <activity
256-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:313:9-319:58
257            android:name="org.autojs.autojs.ui.log.LogActivity"
257-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:314:13-64
258            android:exported="true"
258-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:315:13-36
259            android:label="@string/text_app_shortcut_log_long_label"
259-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:316:13-69
260            android:launchMode="singleTop"
260-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:318:13-43
261            android:taskAffinity="org.autojs.autojs.ui.log.LogActivity"
261-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:317:13-72
262            android:theme="@style/AppTheme.FullScreen" />
262-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:319:13-55
263        <activity
263-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:321:9-327:58
264            android:name="org.autojs.autojs.ui.doc.DocumentationActivity"
264-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:322:13-74
265            android:exported="true"
265-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:323:13-36
266            android:label="@string/text_app_shortcut_docs_long_label"
266-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:324:13-70
267            android:launchMode="singleTop"
267-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:326:13-43
268            android:taskAffinity="org.autojs.autojs.ui.doc.DocumentationActivity"
268-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:325:13-82
269            android:theme="@style/AppTheme.FullScreen" />
269-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:327:13-55
270        <activity
270-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:329:9-331:58
271            android:name="org.autojs.autojs.theme.app.ColorSearchHelpActivity"
271-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:330:13-79
272            android:theme="@style/AppTheme.FullScreen" />
272-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:331:13-55
273        <activity android:name="org.autojs.autojs.ui.shortcut.AppsIconSelectActivity" />
273-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:333:9-89
273-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:333:19-86
274        <activity android:name="org.autojs.autojs.ui.timing.TimedTaskSettingActivity" />
274-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:335:9-89
274-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:335:19-86
275        <activity
275-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:337:9-339:59
276            android:name="org.autojs.autojs.ui.shortcut.ShortcutCreateActivity"
276-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:338:13-80
277            android:theme="@style/AppTheme.Transparent" />
277-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:339:13-56
278        <activity
278-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:341:9-357:20
279            android:name="org.autojs.autojs.external.widget.ScriptWidgetSettingsActivity"
279-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:342:13-90
280            android:exported="false" >
280-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:343:13-37
281            <intent-filter>
281-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:345:13-347:29
282                <action android:name="android.appwidget.action.APPWIDGET_CONFIGURE" />
282-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:346:17-87
282-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:346:25-84
283            </intent-filter>
284            <intent-filter>
284-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:349:13-355:29
285                <action android:name="android.intent.action.CREATE_SHORTCUT" />
285-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:351:17-80
285-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:351:25-77
286
287                <category android:name="android.intent.category.DEFAULT" />
287-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:256:17-76
287-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:256:27-73
288            </intent-filter>
289        </activity>
290
291        <service android:name="org.autojs.autojs.ui.enhancedfloaty.FloatyService" />
291-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:359:9-85
291-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:359:18-82
292        <service
292-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:361:9-363:58
293            android:name="org.autojs.autojs.external.foreground.MainActivityForegroundService"
293-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:362:13-95
294            android:foregroundServiceType="specialUse" />
294-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:363:13-55
295        <service android:name="org.autojs.autojs.external.ScriptExecutionIntentService" />
295-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:365:9-91
295-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:365:18-88
296
297        <activity android:name="org.autojs.autojs.external.tasker.TaskPrefEditActivity" />
297-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:367:9-91
297-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:367:19-88
298
299        <activity-alias
299-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:403:9-415:26
300            android:name="org.autojs.autojs.external.tasker.PluginActivity"
300-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:404:13-76
301            android:exported="true"
301-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:405:13-36
302            android:icon="@drawable/autojs6_material"
302-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:406:13-54
303            android:label="@string/app_name"
303-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:407:13-45
304            android:targetActivity="org.autojs.autojs.external.tasker.TaskPrefEditActivity"
304-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:408:13-92
305            tools:ignore="ExportedActivity" >
305-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:409:13-44
306            <intent-filter>
306-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:411:13-413:29
307                <action android:name="com.twofortyfouram.locale.intent.action.EDIT_SETTING" />
307-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:412:17-95
307-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:412:25-92
308            </intent-filter>
309        </activity-alias>
310
311        <service
311-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:369:9-384:19
312            android:name="org.autojs.autojs.external.tile.LayoutBoundsTile"
312-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:370:13-76
313            android:exported="true"
313-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:371:13-36
314            android:icon="@drawable/ic_circular_menu_bounds"
314-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:372:13-61
315            android:label="@string/text_inspect_layout_bounds"
315-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:373:13-63
316            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE" >
316-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:374:13-77
317            <meta-data
317-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:376:13-378:41
318                android:name="android.service.quicksettings.ACTIVE_TILE"
318-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:377:17-73
319                android:value="false" />
319-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:378:17-38
320
321            <intent-filter>
321-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:380:13-382:29
322                <action android:name="android.service.quicksettings.action.QS_TILE" />
322-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:381:17-87
322-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:381:25-84
323            </intent-filter>
324        </service>
325        <service
325-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:386:9-401:19
326            android:name="org.autojs.autojs.external.tile.LayoutHierarchyTile"
326-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:387:13-79
327            android:exported="true"
327-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:388:13-36
328            android:icon="@drawable/ic_circular_menu_hierarchy"
328-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:389:13-64
329            android:label="@string/text_inspect_layout_hierarchy"
329-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:390:13-66
330            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE" >
330-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:391:13-77
331            <meta-data
331-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:376:13-378:41
332                android:name="android.service.quicksettings.ACTIVE_TILE"
332-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:377:17-73
333                android:value="false" />
333-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:378:17-38
334
335            <intent-filter>
335-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:380:13-382:29
336                <action android:name="android.service.quicksettings.action.QS_TILE" />
336-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:381:17-87
336-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:381:25-84
337            </intent-filter>
338        </service>
339
340        <receiver
340-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:417:9-491:20
341            android:name="org.autojs.autojs.external.receiver.StaticBroadcastReceiver"
341-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:418:13-87
342            android:exported="false" >
342-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:419:13-37
343            <intent-filter>
343-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:421:13-473:29
344                <action android:name="android.intent.action.BOOT_COMPLETED" />
344-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:423:17-79
344-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:423:25-76
345                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
345-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:425:17-82
345-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:425:25-79
346                <action android:name="android.intent.action.TIME_SET" />
346-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:427:17-73
346-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:427:25-70
347                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
347-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:429:17-81
347-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:429:25-78
348                <action android:name="android.intent.action.UID_REMOVED" />
348-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:431:17-76
348-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:431:25-73
349                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
349-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:433:17-87
349-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:433:25-84
350                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
350-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:435:17-90
350-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:435:25-87
351                <action android:name="android.intent.action.ACTION_SHUTDOWN" />
351-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:437:17-80
351-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:437:25-77
352                <action android:name="android.intent.action.ACTION_BATTERY_CHANGED" />
352-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:439:17-87
352-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:439:25-84
353                <action android:name="android.intent.action.DATE_CHANGED" />
353-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:441:17-77
353-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:441:25-74
354                <action android:name="android.intent.action.DREAMING_STARTED" />
354-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:443:17-81
354-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:443:25-78
355                <action android:name="android.intent.action.DREAMING_STOPPED" />
355-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:445:17-81
355-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:445:25-78
356                <action android:name="android.intent.action.HEADSET_PLUG" />
356-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:447:17-77
356-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:447:25-74
357                <action android:name="android.intent.action.INPUT_METHOD_CHANGED" />
357-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:449:17-85
357-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:449:25-82
358                <action android:name="android.intent.action.LOCALE_CHANGED" />
358-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:451:17-79
358-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:451:25-76
359                <action android:name="android.intent.action.MEDIA_BUTTON" />
359-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:453:17-77
359-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:453:25-74
360                <action android:name="android.intent.action.MEDIA_CHECKING" />
360-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:455:17-79
360-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:455:25-76
361                <action android:name="android.intent.action.MEDIA_MOUNTED" />
361-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:457:17-78
361-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:457:25-75
362                <action android:name="android.intent.action.PACKAGE_FIRST_LAUNCH" />
362-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:459:17-85
362-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:459:25-82
363                <action android:name="android.intent.action.PROVIDER_CHANGED" />
363-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:461:17-81
363-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:461:25-78
364                <action android:name="android.intent.action.WALLPAPER_CHANGED" />
364-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:463:17-82
364-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:463:25-79
365                <action android:name="android.intent.action.USER_UNLOCKED" />
365-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:465:17-78
365-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:465:25-75
366                <action android:name="android.intent.action.USER_PRESENT" />
366-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:467:17-77
366-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:467:25-74
367                <action
367-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:469:17-471:50
368                    android:name="android.net.conn.CONNECTIVITY_CHANGE"
368-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:470:21-72
369                    tools:ignore="BatteryLife" />
369-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:471:21-47
370            </intent-filter>
371            <intent-filter>
371-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:475:13-489:29
372                <action android:name="android.intent.action.PACKAGE_ADDED" />
372-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:477:17-78
372-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:477:25-75
373                <action android:name="android.intent.action.PACKAGE_CHANGED" />
373-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:479:17-80
373-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:479:25-77
374                <action android:name="android.intent.action.PACKAGE_DATA_CLEARED" />
374-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:481:17-85
374-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:481:25-82
375                <action android:name="android.intent.action.PACKAGE_REMOVED" />
375-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:483:17-80
375-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:483:25-77
376                <action android:name="android.intent.action.PACKAGE_RESTARTED" />
376-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:485:17-82
376-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:485:25-79
377
378                <data android:scheme="package" />
378-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:487:17-50
378-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:487:23-47
379            </intent-filter>
380        </receiver>
381        <receiver
381-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:493:9-505:20
382            android:name="org.autojs.autojs.external.tasker.FireSettingReceiver"
382-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:494:13-81
383            android:exported="true"
383-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:495:13-36
384            android:process=":background"
384-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:496:13-42
385            tools:ignore="ExportedReceiver" >
385-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:497:13-44
386
387            <!-- this Intent filter allows the plug-in to be discovered by the host. -->
388
389            <intent-filter>
389-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:501:13-503:29
390                <action android:name="com.twofortyfouram.locale.intent.action.FIRE_SETTING" />
390-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:502:17-95
390-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:502:25-92
391            </intent-filter>
392        </receiver>
393
394        <activity
394-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:507:9-531:20
395            android:name="org.autojs.autojs.external.open.EditIntentActivity"
395-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:508:13-78
396            android:exported="true"
396-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:509:13-36
397            android:icon="@drawable/autojs6_material"
397-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:510:13-54
398            android:label="@string/text_edit_script" >
398-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:511:13-53
399            <intent-filter>
399-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:512:13-530:29
400                <action android:name="android.intent.action.VIEW" />
400-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:514:17-69
400-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:514:25-66
401                <action android:name="android.intent.action.EDIT" />
401-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:516:17-69
401-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:516:25-66
402
403                <category android:name="android.intent.category.DEFAULT" />
403-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:256:17-76
403-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:256:27-73
404                <category android:name="android.intent.category.BROWSABLE" />
404-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:520:17-78
404-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:520:27-75
405
406                <data android:scheme="file" />
406-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:487:17-50
406-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:487:23-47
407                <data android:scheme="content" />
407-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:487:17-50
407-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:487:23-47
408                <data android:mimeType="application/x-javascript" />
408-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:487:17-50
408-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:526:23-66
409                <data android:mimeType="text/plain" />
409-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:487:17-50
409-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:526:23-66
410            </intent-filter>
411        </activity>
412        <activity
412-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:533:9-559:20
413            android:name="org.autojs.autojs.external.open.RunIntentActivity"
413-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:534:13-77
414            android:exported="true"
414-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:535:13-36
415            android:icon="@drawable/autojs6_material"
415-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:536:13-54
416            android:label="@string/text_run_script"
416-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:537:13-52
417            android:taskAffinity="org.autojs.autojs.external.open.RunIntentActivity"
417-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:538:13-85
418            android:theme="@android:style/Theme.NoDisplay" >
418-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:539:13-59
419            <intent-filter>
419-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:512:13-530:29
420                <action android:name="android.intent.action.VIEW" />
420-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:514:17-69
420-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:514:25-66
421                <action android:name="android.intent.action.EDIT" />
421-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:516:17-69
421-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:516:25-66
422
423                <category android:name="android.intent.category.DEFAULT" />
423-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:256:17-76
423-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:256:27-73
424                <category android:name="android.intent.category.BROWSABLE" />
424-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:520:17-78
424-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:520:27-75
425
426                <data android:scheme="file" />
426-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:487:17-50
426-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:487:23-47
427                <data android:scheme="content" />
427-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:487:17-50
427-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:487:23-47
428                <data android:mimeType="application/x-javascript" />
428-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:487:17-50
428-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:526:23-66
429                <data android:mimeType="text/plain" />
429-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:487:17-50
429-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:526:23-66
430            </intent-filter>
431        </activity>
432        <activity
432-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:561:9-586:20
433            android:name="org.autojs.autojs.external.open.ImportIntentActivity"
433-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:562:13-80
434            android:exported="true"
434-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:563:13-36
435            android:icon="@drawable/autojs6_material"
435-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:564:13-54
436            android:label="@string/text_import_script"
436-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:565:13-55
437            android:theme="@style/AppTheme.Transparent" >
437-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:566:13-56
438            <intent-filter>
438-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:512:13-530:29
439                <action android:name="android.intent.action.VIEW" />
439-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:514:17-69
439-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:514:25-66
440                <action android:name="android.intent.action.EDIT" />
440-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:516:17-69
440-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:516:25-66
441
442                <category android:name="android.intent.category.DEFAULT" />
442-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:256:17-76
442-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:256:27-73
443                <category android:name="android.intent.category.BROWSABLE" />
443-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:520:17-78
443-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:520:27-75
444
445                <data android:scheme="file" />
445-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:487:17-50
445-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:487:23-47
446                <data android:scheme="content" />
446-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:487:17-50
446-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:487:23-47
447                <data android:mimeType="application/x-javascript" />
447-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:487:17-50
447-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:526:23-66
448                <data android:mimeType="text/plain" />
448-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:487:17-50
448-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:526:23-66
449            </intent-filter>
450        </activity>
451
452        <service
452-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:588:9-596:19
453            android:name="org.autojs.autojs.core.notification.NotificationListenerService"
453-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:589:13-91
454            android:exported="false"
454-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:590:13-37
455            android:label="@string/app_name"
455-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:591:13-45
456            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
456-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:592:13-87
457            <intent-filter>
457-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:593:13-595:29
458                <action android:name="android.service.notification.NotificationListenerService" />
458-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:594:17-99
458-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:594:25-96
459            </intent-filter>
460        </service>
461
462        <receiver
462-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:598:9-610:20
463            android:name="org.autojs.autojs.external.widget.ScriptWidget"
463-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:599:13-74
464            android:exported="false" >
464-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:600:13-37
465            <intent-filter>
465-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:602:13-604:29
466                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
466-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:603:17-84
466-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:603:25-81
467            </intent-filter>
468
469            <meta-data
469-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:606:13-608:64
470                android:name="android.appwidget.provider"
470-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:607:17-58
471                android:resource="@xml/script_widget_config" />
471-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:608:17-61
472        </receiver>
473        <receiver
473-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:612:9-618:20
474            android:name="org.autojs.autojs.timing.TaskReceiver"
474-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:613:13-65
475            android:exported="false" >
475-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:614:13-37
476            <intent-filter>
476-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:615:13-617:29
477                <action android:name="org.autojs.autojs.action.task" />
477-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:616:17-72
477-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:616:25-69
478            </intent-filter>
479        </receiver>
480
481        <!-- <receiver -->
482        <!-- android:name="org.autojs.autojs.ui.main.drawer.DrawerFragment.BatteryChangedReceiver" -->
483        <!-- android:exported="true"> -->
484        <!-- <intent-filter> -->
485        <!-- <action android:name="android.intent.action.ACTION_BATTERY_CHANGED" /> -->
486        <!-- <action android:name="android.intent.action.ACTION_POWER_CONNECTED" /> -->
487        <!-- <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" /> -->
488        <!-- </intent-filter> -->
489        <!-- </receiver> -->
490
491        <activity
491-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:630:9-632:60
492            android:name="org.autojs.autojs.theme.app.ColorSelectActivity"
492-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:631:13-75
493            android:theme="@style/MtAppTheme.FullScreen" />
493-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:632:13-57
494        <activity
494-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:634:9-636:60
495            android:name="org.autojs.autojs.theme.app.ColorLibrariesActivity"
495-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:635:13-78
496            android:theme="@style/MtAppTheme.FullScreen" />
496-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:636:13-57
497        <activity
497-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:638:9-640:60
498            android:name="org.autojs.autojs.theme.app.ColorItemsActivity"
498-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:639:13-74
499            android:theme="@style/MtAppTheme.FullScreen" />
499-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:640:13-57
500        <activity
500-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:642:9-645:50
501            android:name="org.autojs.autojs.execution.ScriptExecuteActivity"
501-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:643:13-77
502            android:configChanges="orientation|keyboardHidden|screenSize|locale"
502-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:644:13-81
503            android:theme="@style/ScriptTheme" />
503-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:645:13-47
504        <activity
504-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:647:9-651:62
505            android:name="org.autojs.autojs.core.permission.PermissionRequestActivity"
505-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:648:13-87
506            android:excludeFromRecents="true"
506-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:649:13-46
507            android:taskAffinity="org.autojs.autojs.core.permission.PermissionRequestActivity"
507-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:650:13-95
508            android:theme="@style/ScriptTheme.Transparent" />
508-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:651:13-59
509        <activity
509-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:653:9-657:62
510            android:name="org.autojs.autojs.core.activity.StartForResultActivity"
510-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:654:13-82
511            android:excludeFromRecents="true"
511-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:655:13-46
512            android:taskAffinity="org.autojs.autojs.core.activity.StartForResultActivity"
512-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:656:13-90
513            android:theme="@style/ScriptTheme.Transparent" />
513-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:657:13-59
514
515        <service
515-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:659:9-672:19
516            android:name="org.autojs.autojs.core.accessibility.AccessibilityServiceUsher"
516-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:660:13-90
517            android:exported="true"
517-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:662:13-36
518            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
518-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:661:13-79
519            <intent-filter>
519-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:664:13-666:29
520                <action android:name="android.accessibilityservice.AccessibilityService" />
520-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:665:17-92
520-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:665:25-89
521            </intent-filter>
522
523            <meta-data
523-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:668:13-670:72
524                android:name="android.accessibilityservice"
524-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:669:17-60
525                android:resource="@xml/accessibility_service_config" />
525-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:670:17-69
526        </service>
527        <service
527-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:674:9-676:63
528            android:name="org.autojs.autojs.core.image.capture.ScreenCapturerForegroundService"
528-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:675:13-96
529            android:foregroundServiceType="mediaProjection" />
529-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:676:13-60
530        <service
530-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:678:9-685:19
531            android:name="androidx.appcompat.app.AppLocalesMetadataHolderService"
531-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:679:13-82
532            android:enabled="false"
532-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:680:13-36
533            android:exported="false" >
533-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:681:13-37
534            <meta-data
534-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:682:13-684:40
535                android:name="autoStoreLocales"
535-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:683:17-48
536                android:value="true" />
536-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:684:17-37
537        </service>
538        <service android:name="org.eclipse.paho.android.service.MqttService" />
538-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:687:9-80
538-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:687:18-77
539
540        <activity
540-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:689:9-700:20
541            android:name="org.autojs.autojs.inrt.SplashActivity"
541-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:690:13-65
542            android:exported="true"
542-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:691:13-36
543            android:theme="@style/AppTheme.Splash" >
543-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:692:13-51
544            <intent-filter tools:ignore="ExtraText" >
544-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:693:13-699:29
544-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:693:28-52
545                <action android:name="android.intent.action.MAIN" />
545-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:218:17-69
545-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:218:25-66
546
547                <category android:name="${intentCategoryInrt}" />
547-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:697:17-66
547-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:697:27-63
548            </intent-filter>
549        </activity>
550        <activity
550-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:702:9-704:63
551            android:name="org.autojs.autojs.inrt.LogActivity"
551-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:703:13-62
552            android:theme="@style/AppTheme.NoActionBarInrt" />
552-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:704:13-60
553        <activity
553-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:706:9-708:63
554            android:name="org.autojs.autojs.inrt.SettingsActivity"
554-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:707:13-67
555            android:theme="@style/AppTheme.NoActionBarInrt" />
555-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:708:13-60
556
557        <!-- This provider is required by Shizuku, remove this if your app only supports Sui -->
558        <provider
558-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:711:9-717:82
559            android:name="rikka.shizuku.ShizukuProvider"
559-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:712:13-57
560            android:authorities="${applicationId}.shizuku"
560-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:713:13-59
561            android:enabled="true"
561-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:714:13-35
562            android:exported="true"
562-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:715:13-36
563            android:multiprocess="false"
563-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:716:13-41
564            android:permission="android.permission.INTERACT_ACROSS_USERS_FULL" />
564-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:717:13-79
565
566        <meta-data
566-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:719:9-721:36
567            android:name="moe.shizuku.client.V3_SUPPORT"
567-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:720:13-57
568            android:value="true" />
568-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\AndroidManifest.xml:721:13-33
569    </application>
570
571</manifest>
