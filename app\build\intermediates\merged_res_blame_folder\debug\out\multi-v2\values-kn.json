{"logs": [{"outputFile": "com.bm.atool.app-mergeDebugResources-43:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\29973079252e5e41b13d785bb69b8fcd\\transformed\\material-1.5.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,306,413,526,611,674,768,834,896,999,1070,1129,1205,1270,1324,1437,1495,1556,1610,1689,1805,1888,1979,2091,2170,2249,2337,2404,2470,2550,2640,2724,2801,2878,2955,3024,3123,3200,3293,3388,3462,3543,3639,3690,3758,3844,3932,3995,4060,4123,4228,4331,4426,4531", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,106,112,84,62,93,65,61,102,70,58,75,64,53,112,57,60,53,78,115,82,90,111,78,78,87,66,65,79,89,83,76,76,76,68,98,76,92,94,73,80,95,50,67,85,87,62,64,62,104,102,94,104,81", "endOffsets": "219,301,408,521,606,669,763,829,891,994,1065,1124,1200,1265,1319,1432,1490,1551,1605,1684,1800,1883,1974,2086,2165,2244,2332,2399,2465,2545,2635,2719,2796,2873,2950,3019,3118,3195,3288,3383,3457,3538,3634,3685,3753,3839,3927,3990,4055,4118,4223,4326,4421,4526,4608"}, "to": {"startLines": "2,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3025,3852,3959,4072,4227,4376,4470,4536,4598,4701,4772,4831,4907,4972,5026,5139,5197,5258,5312,5391,5507,5590,5681,5793,5872,5951,6039,6106,6172,6252,6342,6426,6503,6580,6657,6726,6825,6902,6995,7090,7164,7245,7341,7392,7460,7546,7634,7697,7762,7825,7930,8033,8128,8557", "endLines": "5,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "endColumns": "12,81,106,112,84,62,93,65,61,102,70,58,75,64,53,112,57,60,53,78,115,82,90,111,78,78,87,66,65,79,89,83,76,76,76,68,98,76,92,94,73,80,95,50,67,85,87,62,64,62,104,102,94,104,81", "endOffsets": "269,3102,3954,4067,4152,4285,4465,4531,4593,4696,4767,4826,4902,4967,5021,5134,5192,5253,5307,5386,5502,5585,5676,5788,5867,5946,6034,6101,6167,6247,6337,6421,6498,6575,6652,6721,6820,6897,6990,7085,7159,7240,7336,7387,7455,7541,7629,7692,7757,7820,7925,8028,8123,8228,8634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bca5d58794b22aca6036379432e392dd\\transformed\\navigation-ui-2.7.7\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,173", "endColumns": "117,128", "endOffsets": "168,297"}, "to": {"startLines": "95,96", "startColumns": "4,4", "startOffsets": "8233,8351", "endColumns": "117,128", "endOffsets": "8346,8475"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4b1b6d71ac725a2fc4ab91a425e45a1c\\transformed\\appcompat-1.7.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,529,636,762,840,916,1007,1100,1195,1289,1389,1482,1577,1671,1762,1853,1935,2051,2161,2260,2373,2478,2592,2756,2856", "endColumns": "113,111,112,84,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,524,631,757,835,911,1002,1095,1190,1284,1384,1477,1572,1666,1757,1848,1930,2046,2156,2255,2368,2473,2587,2751,2851,2934"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "274,388,500,613,698,805,931,1009,1085,1176,1269,1364,1458,1558,1651,1746,1840,1931,2022,2104,2220,2330,2429,2542,2647,2761,2925,8788", "endColumns": "113,111,112,84,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "383,495,608,693,800,926,1004,1080,1171,1264,1359,1453,1553,1646,1741,1835,1926,2017,2099,2215,2325,2424,2537,2642,2756,2920,3020,8866"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\cee19163c10f1fafd81d90a045427d43\\transformed\\core-1.13.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "34,35,36,37,38,39,40,101", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3107,3205,3308,3409,3515,3616,3724,8871", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "3200,3303,3404,3510,3611,3719,3847,8967"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\466bd753c0bc663f243f57f85dfa0cbd\\transformed\\preference-1.2.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,261,338,487,656,736", "endColumns": "69,85,76,148,168,79,76", "endOffsets": "170,256,333,482,651,731,808"}, "to": {"startLines": "44,46,97,99,102,103,104", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4157,4290,8480,8639,8972,9141,9221", "endColumns": "69,85,76,148,168,79,76", "endOffsets": "4222,4371,8552,8783,9136,9216,9293"}}]}]}