{"logs": [{"outputFile": "com.bm.atool.app-mergeDebugResources-43:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\bca5d58794b22aca6036379432e392dd\\transformed\\navigation-ui-2.7.7\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "95,96", "startColumns": "4,4", "startOffsets": "8186,8298", "endColumns": "111,119", "endOffsets": "8293,8413"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\cee19163c10f1fafd81d90a045427d43\\transformed\\core-1.13.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "34,35,36,37,38,39,40,101", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3099,3196,3298,3400,3501,3604,3711,8806", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "3191,3293,3395,3496,3599,3706,3816,8902"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4b1b6d71ac725a2fc4ab91a425e45a1c\\transformed\\appcompat-1.7.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "268,376,468,583,667,782,905,982,1057,1148,1241,1336,1430,1530,1623,1718,1813,1904,1995,2078,2188,2298,2398,2509,2618,2737,2919,8722", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "371,463,578,662,777,900,977,1052,1143,1236,1331,1425,1525,1618,1713,1808,1899,1990,2073,2183,2293,2393,2504,2613,2732,2914,3017,8801"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\466bd753c0bc663f243f57f85dfa0cbd\\transformed\\preference-1.2.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,264,346,483,652,731", "endColumns": "70,87,81,136,168,78,75", "endOffsets": "171,259,341,478,647,726,802"}, "to": {"startLines": "44,46,97,99,102,103,104", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4123,4258,8418,8585,8907,9076,9155", "endColumns": "70,87,81,136,168,78,75", "endOffsets": "4189,4341,8495,8717,9071,9150,9226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\29973079252e5e41b13d785bb69b8fcd\\transformed\\material-1.5.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,218,295,394,514,597,661,760,835,894,1004,1073,1131,1203,1264,1319,1422,1479,1539,1594,1675,1795,1878,1966,2071,2154,2234,2328,2395,2461,2537,2619,2705,2782,2857,2936,3013,3109,3186,3278,3375,3449,3534,3631,3683,3750,3838,3925,3987,4051,4114,4212,4309,4403,4501", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,98,119,82,63,98,74,58,109,68,57,71,60,54,102,56,59,54,80,119,82,87,104,82,79,93,66,65,75,81,85,76,74,78,76,95,76,91,96,73,84,96,51,66,87,86,61,63,62,97,96,93,97,84", "endOffsets": "213,290,389,509,592,656,755,830,889,999,1068,1126,1198,1259,1314,1417,1474,1534,1589,1670,1790,1873,1961,2066,2149,2229,2323,2390,2456,2532,2614,2700,2777,2852,2931,3008,3104,3181,3273,3370,3444,3529,3626,3678,3745,3833,3920,3982,4046,4109,4207,4304,4398,4496,4581"}, "to": {"startLines": "2,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3022,3821,3920,4040,4194,4346,4445,4520,4579,4689,4758,4816,4888,4949,5004,5107,5164,5224,5279,5360,5480,5563,5651,5756,5839,5919,6013,6080,6146,6222,6304,6390,6467,6542,6621,6698,6794,6871,6963,7060,7134,7219,7316,7368,7435,7523,7610,7672,7736,7799,7897,7994,8088,8500", "endLines": "5,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "endColumns": "12,76,98,119,82,63,98,74,58,109,68,57,71,60,54,102,56,59,54,80,119,82,87,104,82,79,93,66,65,75,81,85,76,74,78,76,95,76,91,96,73,84,96,51,66,87,86,61,63,62,97,96,93,97,84", "endOffsets": "263,3094,3915,4035,4118,4253,4440,4515,4574,4684,4753,4811,4883,4944,4999,5102,5159,5219,5274,5355,5475,5558,5646,5751,5834,5914,6008,6075,6141,6217,6299,6385,6462,6537,6616,6693,6789,6866,6958,7055,7129,7214,7311,7363,7430,7518,7605,7667,7731,7794,7892,7989,8083,8181,8580"}}]}]}